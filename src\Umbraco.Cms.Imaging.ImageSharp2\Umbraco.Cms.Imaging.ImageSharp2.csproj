<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Umbraco CMS - Imaging - ImageSharp 2</Title>
    <Description>Adds imaging support using ImageSharp/ImageSharp.Web version 2 to Umbraco CMS.</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SixLabors.ImageSharp" VersionOverride="[2.1.10, 3)" />
    <PackageReference Include="SixLabors.ImageSharp.Web" VersionOverride="[2.0.2, 3)" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Umbraco.Web.Common\Umbraco.Web.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Umbraco.Tests.UnitTests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
</Project>
