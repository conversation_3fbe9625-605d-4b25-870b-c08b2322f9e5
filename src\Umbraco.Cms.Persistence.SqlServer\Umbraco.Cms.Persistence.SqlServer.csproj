<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Umbraco CMS - Persistence - SQL Server</Title>
    <Description>Adds support for SQL Server to Umbraco CMS.</Description>
  </PropertyGroup>

  <ItemGroup>
    <!-- Take top-level depedendency on Azure.Identity, because NPoco.SqlServer depends on a vulnerable version -->
    <PackageReference Include="Azure.Identity" />
    <!-- Both Azure.Identity, Microsoft.EntityFrameworkCore.SqlServer,NPoco.SqlServer, and more bring in a vulnerable version of System.Text.Json -->
    <PackageReference Include="System.Text.Json" />
    <!-- Both Microsoft.EntityFrameworkCore.SqlServer and NPoco.SqlServer bring in a vulnerable version of Microsoft.Data.SqlClient -->
    <PackageReference Include="Microsoft.Data.SqlClient" />

    <PackageReference Include="NPoco.SqlServer" />

    <!-- Both OpenIddict.AspNetCore, Npoco.SqlServer and Microsoft.EntityFrameworkCore.SqlServer bring in a vulnerable version of Microsoft.IdentityModel.JsonWebTokens -->
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Umbraco.Infrastructure\Umbraco.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Umbraco.Tests.Integration</_Parameter1>
    </AssemblyAttribute>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Umbraco.Tests.UnitTests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
</Project>
