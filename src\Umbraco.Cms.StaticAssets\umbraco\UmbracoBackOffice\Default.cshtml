@using Microsoft.Extensions.Options;
@using System.Globalization
@using Umbraco.Cms.Core.Configuration
@using Umbraco.Cms.Core.Configuration.Models
@using Umbraco.Cms.Core.Hosting
@using Umbraco.Cms.Core.Logging
@using Umbraco.Cms.Core.Routing
@using Umbraco.Cms.Core.WebAssets
@using Umbraco.Cms.Infrastructure.WebAssets
@using Umbraco.Cms.Web.BackOffice.Controllers
@using Umbraco.Cms.Web.BackOffice.Security
@using Umbraco.Extensions
@inject BackOfficeServerVariables backOfficeServerVariables
@inject IUmbracoVersion umbracoVersion
@inject IHostingEnvironment hostingEnvironment
@inject IOptions<GlobalSettings> globalSettings
@inject IRuntimeMinifier runtimeMinifier
@inject IProfilerHtml profilerHtml
@inject IBackOfficeExternalLoginProviders externalLogins
@{
    bool.TryParse(Context.Request.Query["umbDebug"], out bool isDebug);
    var backOfficePath = globalSettings.Value.GetBackOfficePath(hostingEnvironment);
}

<!DOCTYPE html>

<html lang="@CultureInfo.CurrentCulture.Name.ToLowerInvariant()">
<head>
    <base href="@backOfficePath.EnsureEndsWith('/')" />
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="robots" content="noindex, nofollow">
    <meta name="pinterest" content="nopin" />

    <title ng-bind="$root.locationTitle">Umbraco</title>

    @Html.Raw(await runtimeMinifier.RenderCssHereAsync(BackOfficeWebAssets.UmbracoInitCssBundleName))
</head>
<body ng-class="{'touch':touchDevice, 'emptySection':emptySection, 'umb-drawer-is-visible':drawer.show, 'umb-tour-is-visible': tour.show, 'tabbing-active':tabbingActive}" ng-controller="Umbraco.MainController" id="umbracoMainPageBody">
    <noscript>
        <div class="flex flex-wrap flex-column items-center justify-center" style="height: 100%">
            <h1 class="h3" style="display: inline-flex; align-items: center; gap: 10px">
                <img aria-hidden="true" alt="logo" src="~/umbraco/assets/img/application/logo.svg" style="width: 30px" />
                <span>Umbraco</span>
            </h1>
            <p>For full functionality of Umbraco CMS it is necessary to enable JavaScript.</p>
            <p>Here are the <a href="https://www.enable-javascript.com/" target="_blank" rel="noopener" style="text-decoration: underline;">instructions how to enable JavaScript in your web browser</a>.</p>
        </div>
    </noscript>

    <umb-backoffice-icon-registry>
        <div ng-hide="!authenticated" ng-cloak>

            <div style="display: none;" id="mainwrapper" class="clearfix">

                <umb-app-header></umb-app-header>

                <div class="umb-app-content">

                    <umb-navigation></umb-navigation>

                    <section id="contentwrapper">

                        <div id="contentcolumn">
                            <div class="umb-editor" ng-view></div>
                        </div>

                    </section>

                </div>

            </div>

            <umb-tour ng-if="tour.show"
                      model="tour">
            </umb-tour>

            <!-- help dialog controller by the help button - this also forces the backoffice UI to shift 400px  -->
            <umb-drawer data-element="drawer" ng-if="drawer.show" model="drawer.model" view="drawer.view"></umb-drawer>

            <umb-search ng-attr-umb-focus-lock="true" ng-if="search.show" on-close="closeSearch()"></umb-search>

        </div>

        <umb-notifications></umb-notifications>

        <umb-backdrop ng-if="backdrop.show || infiniteMode"
                      backdrop-opacity="backdrop.opacity"
                      highlight-element="backdrop.element"
                      highlight-prevent-click="backdrop.elementPreventClick"
                      disable-events-on-click="backdrop.disableEventsOnClick">
        </umb-backdrop>

        <umb-overlay ng-if="overlay.show"
                     model="overlay"
                     position="{{overlay.position}}"
                     size="overlay.size"
                     view="overlay.view"
                     name="overlay.name"
                     parent-scope="overlay.parentScope">
        </umb-overlay>

        <umb-editors ng-show="infiniteMode"></umb-editors>

        <umb-login ng-if="login.show"
                   is-timed-out="login.isTimedOut"
                   on-login="hideLoginScreen()">
        </umb-login>
    </umb-backoffice-icon-registry>

    @await Html.BareMinimumServerVariablesScriptAsync(backOfficeServerVariables)

    <script>
        document.angularReady = function(app) {
            @await Html.AngularValueExternalLoginInfoScriptAsync(externalLogins, ViewData.GetExternalSignInProviderErrors()!)
            @Html.AngularValueResetPasswordCodeInfoScript(ViewData[ViewDataExtensions.TokenPasswordResetCode]!)
            @await Html.AngularValueTinyMceAssetsAsync(runtimeMinifier)
            //required for the noscript trick
            document.getElementById("mainwrapper").style.display = "inherit";
        }
    </script>

    <script src="@WebPath.Combine(backOfficePath.TrimStart("~"), "/lib/lazyload-js/LazyLoad.min.js")"></script>
    <script src="@Url.GetUrlWithCacheBust("Application", "BackOffice", null!, hostingEnvironment, umbracoVersion, runtimeMinifier)"></script>

    @if (isDebug)
    {
        @Html.Raw(profilerHtml.Render())
    }

</body>
</html>

