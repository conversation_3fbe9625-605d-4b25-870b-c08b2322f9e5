<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Umbraco CMS - Persistence - Entity Framework Core</Title>
    <Description>Adds support for Entity Framework Core to Umbraco CMS.</Description>
  </PropertyGroup>

  <ItemGroup>
    <!-- Take top-level depedendency on Azure.Identity, because Microsoft.EntityFrameworkCore.SqlServer depends on a vulnerable version -->
    <PackageReference Include="Azure.Identity" />

    <!-- Take top-level depedendency on Microsoft.Extensions.Caching.Memory, because Microsoft.EntityFrameworkCore.* depends on a vulnerable version -->
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" />
    <!-- Both Azure.Identity, Microsoft.EntityFrameworkCore.SqlServer,NPoco.SqlServer, and more bring in a vulnerable version of System.Text.Json -->
    <PackageReference Include="System.Text.Json" />
    <!-- Both Microsoft.EntityFrameworkCore.SqlServer and NPoco.SqlServer bring in a vulnerable version of Microsoft.Data.SqlClient -->
    <PackageReference Include="Microsoft.Data.SqlClient" />

    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="OpenIddict.EntityFrameworkCore" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Umbraco.Core\Umbraco.Core.csproj" />
    <ProjectReference Include="..\Umbraco.Infrastructure\Umbraco.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Umbraco.Tests.Integration</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
</Project>
