<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Umbraco CMS - Persistence - Entity Framework Core - SQL Server migrations</Title>
    <Description>Adds support for Entity Framework Core SQL Server migrations to Umbraco CMS.</Description>
  </PropertyGroup>

  <ItemGroup>
    <!-- Take top-level depedendency on Azure.Identity, because Microsoft.EntityFrameworkCore.SqlServer depends on a vulnerable version -->
    <PackageReference Include="Azure.Identity" />
    <!-- Both Azure.Identity, Microsoft.EntityFrameworkCore.SqlServer,NPoco.SqlServer, and more bring in a vulnerable version of System.Text.Json -->
    <PackageReference Include="System.Text.Json" />
    <!-- Both Microsoft.EntityFrameworkCore.SqlServer and NPoco.SqlServer bring in a vulnerable version of Microsoft.Data.SqlClient -->
    <PackageReference Include="Microsoft.Data.SqlClient" />

    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />

    <!-- Both OpenIddict.AspNetCore, Npoco.SqlServer and Microsoft.EntityFrameworkCore.SqlServer bring in a vulnerable version of Microsoft.IdentityModel.JsonWebTokens -->
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Umbraco.Cms.Persistence.EFCore\Umbraco.Cms.Persistence.EFCore.csproj" />
  </ItemGroup>
</Project>
