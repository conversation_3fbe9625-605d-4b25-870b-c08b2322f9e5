using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using Umbraco.Cms.Core.Serialization;

namespace Umbraco10.Serializers
{
    public class ResilientJsonSerializer : IJsonSerializer
    {
        private readonly IJsonSerializer _innerSerializer;
        private readonly ILogger<ResilientJsonSerializer> _logger;

        public ResilientJsonSerializer(IJsonSerializer innerSerializer, ILogger<ResilientJsonSerializer> logger)
        {
            _innerSerializer = innerSerializer;
            _logger = logger;
        }

        public string Serialize(object? input)
        {
            return _innerSerializer.Serialize(input);
        }

        public T? Deserialize<T>(string input)
        {
            try
            {
                return _innerSerializer.Deserialize<T>(input);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "JSON deserialization error for type {Type}. Input: {Input}", typeof(T).Name, input);
                
                // Handle specific types
                if (typeof(T) == typeof(IEnumerable<object>) || typeof(T) == typeof(object[]))
                {
                    _logger.LogInformation("Returning empty array for collection type {Type}", typeof(T).Name);
                    return (T)(object)Array.Empty<object>();
                }
                
                if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(IEnumerable<>))
                {
                    Type elementType = typeof(T).GetGenericArguments()[0];
                    var listType = typeof(List<>).MakeGenericType(elementType);
                    return (T)Activator.CreateInstance(listType);
                }
                
                if (typeof(T) == typeof(JArray))
                {
                    return (T)(object)new JArray();
                }
                
                if (typeof(T) == typeof(JObject))
                {
                    return (T)(object)new JObject();
                }
                
                // For other types, return default
                return default;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error deserializing JSON for type {Type}", typeof(T).Name);
                return default;
            }
        }

        public T? DeserializeSubset<T>(string input, string key)
        {
            try
            {
                return _innerSerializer.DeserializeSubset<T>(input, key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deserializing JSON subset with key {Key} for type {Type}", key, typeof(T).Name);
                return default;
            }
        }
    }
}