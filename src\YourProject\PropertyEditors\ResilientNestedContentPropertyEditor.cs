using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Umbraco.Cms.Core.IO;
using Umbraco.Cms.Core.PropertyEditors;
using Umbraco.Cms.Core.Services;
using Umbraco.Extensions;

namespace Umbraco10.Editors
{
    [DataEditor(
        "YourProject.ResilientNestedContent",
        "Resilient Nested Content",
        "nestedcontent",
        ValueType = ValueTypes.Json,
        Group = Constants.PropertyEditors.Groups.Lists,
        Icon = "icon-thumbnail-list",
        ValueEditorIsReusable = false)]
    public class ResilientNestedContentPropertyEditor : NestedContentPropertyEditor
    {
        private readonly ILogger<ResilientNestedContentPropertyEditor> _logger;

        public ResilientNestedContentPropertyEditor(
            IDataValueEditorFactory dataValueEditorFactory,
            IIOHelper ioHelper,
            IEditorConfigurationParser editorConfigurationParser,
            INestedContentPropertyIndexValueFactory nestedContentPropertyIndexValueFactory,
            ILogger<ResilientNestedContentPropertyEditor> logger)
            : base(dataValueEditorFactory, i<PERSON><PERSON><PERSON><PERSON>, editor<PERSON>onfigurationPars<PERSON>, nestedContentPropertyIndexValueFactory)
        {
            _logger = logger;
        }

        protected override IDataValueEditor CreateValueEditor()
        {
            return DataValueEditorFactory.Create<ResilientNestedContentPropertyValueEditor>(Attribute!);
        }

        internal class ResilientNestedContentPropertyValueEditor : NestedContentPropertyValueEditor
        {
            private readonly ILogger<ResilientNestedContentPropertyEditor> _logger;

            public ResilientNestedContentPropertyValueEditor(
                IDataTypeConfigurationCache dataTypeReadCache,
                ILocalizedTextService localizedTextService,
                IContentTypeService contentTypeService,
                IShortStringHelper shortStringHelper,
                DataEditorAttribute attribute,
                PropertyEditorCollection propertyEditors,
                DataValueReferenceFactoryCollection dataValueReferenceFactories,
                ILogger<NestedContentPropertyEditor> logger,
                IJsonSerializer jsonSerializer,
                IIOHelper ioHelper,
                IPropertyValidationService propertyValidationService,
                ILogger<ResilientNestedContentPropertyEditor> resilientLogger)
                : base(dataTypeReadCache, localizedTextService, contentTypeService, shortStringHelper, attribute, 
                      propertyEditors, dataValueReferenceFactories, logger, jsonSerializer, ioHelper, propertyValidationService)
            {
                _logger = resilientLogger;
            }

            public override object ToEditor(IProperty property, string? culture = null, string? segment = null)
            {
                try
                {
                    return base.ToEditor(property, culture, segment);
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Error deserializing Nested Content for property {PropertyAlias} on content {ContentId}. Returning empty array.", 
                        property.Alias, property.ContentId);
                    return Array.Empty<object>();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error in Nested Content ToEditor for property {PropertyAlias}. Returning empty array.", 
                        property.Alias);
                    return Array.Empty<object>();
                }
            }
        }
    }
}