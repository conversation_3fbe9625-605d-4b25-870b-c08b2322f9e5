<?xml version="1.0" encoding="utf-8"?>
<language alias="uk" intName="Ukrainian" localName="Українська" lcid="" culture="uk-UA">
  <creator>
    <name>The Umbraco community</name>
    <link>https://docs.umbraco.com/umbraco-cms/extending/language-files</link>
  </creator>
  <area alias="actions">
    <key alias="assigndomain">Мови та домени</key>
    <key alias="auditTrail">Історія виправлень</key>
    <key alias="browse">Переглянути</key>
    <key alias="changeDocType">Змінити тип документу</key>
    <key alias="copy">Копіювати</key>
    <key alias="create">Створити</key>
    <key alias="createblueprint">Створити шаблон вмістимого</key>
    <key alias="createGroup">Створити групу</key>
    <key alias="createPackage">Створити пакет</key>
    <key alias="defaultValue">Значення за замовчуванням</key>
    <key alias="delete">Видалити</key>
    <key alias="disable">Відключити</key>
    <key alias="emptyrecyclebin">Очистити корзину</key>
    <key alias="enable">Включити</key>
    <key alias="export">Експорт</key>
    <key alias="exportDocumentType">Експортувати</key>
    <key alias="importdocumenttype">Імпортувати</key>
    <key alias="importPackage">Імпортувати пакет</key>
    <key alias="liveEdit">Правити на місці</key>
    <key alias="logout">Вийти</key>
    <key alias="move">Перемістити</key>
    <key alias="notify">Сповіщення</key>
    <key alias="protect">Публічний доступ</key>
    <key alias="publish">Опублікувати</key>
    <key alias="refreshNode">Оновити вузли</key>
    <key alias="rename" version="7.3.0">Переіменувати</key>
    <key alias="republish">Опублікувати весь сайт</key>
    <key alias="restore" version="7.3.0">Відновити</key>
    <key alias="rights">Дозволи</key>
    <key alias="rollback">Відкатити</key>
    <key alias="sendtopublish">Надіслати на публікацію</key>
    <key alias="sendToTranslate">Надіслати на переклад</key>
    <key alias="setGroup">Задати групу</key>
    <key alias="setPermissions">Задати права</key>
    <key alias="sort">Сортувати</key>
    <key alias="translate">Перекласти</key>
    <key alias="unpublish">Скасувати публікацію</key>
    <key alias="unlock">Разблокувати</key>
    <key alias="update">Оновити</key>
  </area>
  <area alias="actionCategories">
    <key alias="content">Вміст</key>
    <key alias="administration">Адміністрування</key>
    <key alias="structure">Структура</key>
    <key alias="other">Інше</key>
  </area>
  <area alias="actionDescriptions">
    <key alias="assignDomain">Дозволити доступ до призначення мов і доменів</key>
    <key alias="auditTrail">Дозволити доступ до журналу історії вузла</key>
    <key alias="browse">Дозволити доступ до перегляду вузла</key>
    <key alias="changeDocType">Дозволити доступ до зміни типу документа для вузла</key>
    <key alias="copy">Дозволити доступ до копіювання вузла</key>
    <key alias="create">Дозволити доступ до створення вузлів</key>
    <key alias="delete">Дозволити доступ до видалення вузлів</key>
    <key alias="move">Дозволити доступ до переміщення вузла</key>
    <key alias="protect">Дозволити доступ до встановлення та зміни правил публічного доступу для вузла</key>
    <key alias="publish">Дозволити доступ до публікації вузла</key>
    <key alias="rights">Дозволити доступ до зміни прав доступу до вузла</key>
    <key alias="rollback">Дозволити доступ на повернення до попередніх станів вузла</key>
    <key alias="sendtopublish">Дозволити доступ до відправки вузла на схвалення перед публікацією</key>
    <key alias="sendToTranslate">Дозволити доступ до відправки вузла на переклад даних</key>
    <key alias="sort">Дозволити доступ до зміни порядку сортування вузлів</key>
    <key alias="translate">Дозволити доступ до перекладу даних вузла</key>
    <key alias="update">Дозволити доступ до збереження вузла</key>
    <key alias="createblueprint">Дозволити доступ до створення шаблону вмісту</key>
  </area>
  <area alias="assignDomain">
    <key alias="addNew">Додати новий домен</key>
    <key alias="domain">Домен</key>
    <key alias="domainCreated">Створено новий домен '%0%'</key>
    <key alias="domainDeleted">Домен '%0%' видалено</key>
    <key alias="domainExists">Домен із іменем '%0%' вже існує</key>
    <key alias="domainUpdated">Домен '%0%' оновлено</key>
    <key alias="duplicateDomain">Такий домен вже призначено.</key>
    <key alias="inherit">Успадкувати</key>
    <key alias="invalidDomain">Неприпустимий формат домену.</key>
    <key alias="invalidNode">Неприпустимий вузол.</key>
    <key alias="language">Мова</key>
    <key alias="orEdit">Редагувати існуючі домени</key>
    <key alias="permissionDenied">Недостатньо повноважень.</key>
    <key alias="remove">видалити</key>
    <key alias="setDomains">Домени</key>
    <key alias="setLanguage">Мова (культура)</key>
    <key alias="setLanguageHelp"><![CDATA[Встановіть мову (культуру) для всіх дочірніх вузлів,<br /> бо успадкуйте мову від батьківських вузлів.<br />
		Це налаштування буде застосовано також і до поточного вузла, якщо для нього нижче явно не заданий домен.]]></key>
  </area>
  <area alias="auditTrails">
    <key alias="atViewingFor">Спостерігати за</key>
  </area>
  <area alias="blueprints">
    <key alias="createBlueprintFrom"><![CDATA[Створити новий шаблон вмісту з <em>%0%</em>]]></key>
    <key alias="blankBlueprint">Порожній</key>
    <key alias="selectBlueprint">Вибрати шаблон вмісту</key>
    <key alias="createdBlueprintHeading">Шаблон вмісту створено</key>
    <key alias="createdBlueprintMessage">Створено шаблон вмісту з '%0%'</key>
    <key alias="duplicateBlueprintMessage">Інший шаблон вмісту з такою самою назвою вже існує</key>
    <key alias="blueprintDescription">Шаблон вмісту — це набір даних, який редактор може використовувати для початкового заповнення властивостей при створенні вузлів вмісту.</key>
  </area>
  <area alias="bulk">
    <key alias="done">Завершено</key>
    <key alias="deletedItem">Видалений %0% елемент</key>
    <key alias="deletedItems">Видалено %0% елементів</key>
    <key alias="deletedItemOfItem">Видалений %0% з %1% елементів</key>
    <key alias="deletedItemOfItems">Видалено %0% з %1% елементів</key>
    <key alias="publishedItem">Опублікований %0% елемент</key>
    <key alias="publishedItems">Опубліковано %0% елементів</key>
    <key alias="publishedItemOfItem">Опублікований %0% з %1% елементів</key>
    <key alias="publishedItemOfItems">Опубліковано %0% з %1% елементів</key>
    <key alias="unpublishedItem">Скасовано публікацію %0% елементу</key>
    <key alias="unpublishedItems">Скасовано публікацію %0% елементів</key>
    <key alias="unpublishedItemOfItem">Скасовано публікацію %0% з %1% елементів</key>
    <key alias="unpublishedItemOfItems">Скасовано публікацію %0% з %1% елементів</key>
    <key alias="movedItem">Перенесений %0% елемент</key>
    <key alias="movedItems">Перенесено %0% елементів</key>
    <key alias="movedItemOfItem">Перенесений %0% з %1% елементів</key>
    <key alias="movedItemOfItems">Перенесено %0% з %1% елементів</key>
    <key alias="copiedItem">Скопійований %0% елемент</key>
    <key alias="copiedItems">Скопійовано %0% елементів</key>
    <key alias="copiedItemOfItem">Скопійований %0% з %1% елементів</key>
    <key alias="copiedItemOfItems">Скопійовано %0% з %1% елементів</key>
  </area>
  <area alias="buttons">
    <key alias="bold">Напівжирний</key>
    <key alias="clearSelection">Очистити виділення</key>
    <key alias="deindent">Зменшити відступ</key>
    <key alias="formFieldInsert">Вставити поле форми</key>
    <key alias="graphicHeadline">Вставити графічний заголовок</key>
    <key alias="htmlEdit">Редагувати код HTML</key>
    <key alias="indent">Збільшити відступ</key>
    <key alias="italic">Курсив</key>
    <key alias="justifyCenter">По центру</key>
    <key alias="justifyLeft">Ліворуч</key>
    <key alias="justifyRight">Праворуч</key>
    <key alias="linkInsert">Вставити посилання</key>
    <key alias="linkLocal">Вставити якір (локальне посилання)</key>
    <key alias="listBullet">Маркований список</key>
    <key alias="listNumeric">Нумерований список</key>
    <key alias="macroInsert">Вставити макрос</key>
    <key alias="pictureInsert">Вставити зображення</key>
    <key alias="redo">Повторити</key>
    <key alias="relations">Правити зв'язки</key>
    <key alias="returnToList">Повернутись до списку</key>
    <key alias="save">Зберегти</key>
    <key alias="saveAndGenerateModels">Зберегти та побудувати моделі</key>
    <key alias="saveAndPublish">Опублікувати</key>
    <key alias="saveToPublish">Надіслати на публікацію</key>
    <key alias="saveListView">Зберегти список</key>
    <key alias="select">Вибрати</key>
    <key alias="saveAndPreview">Попередній перегляд</key>
    <key alias="showPageDisabled">Попередній перегляд заборонено, тому що документу не зіставлено шаблон</key>
    <key alias="somethingElse">Інші дії</key>
    <key alias="styleChoose">Вибрати стиль</key>
    <key alias="styleShow">Показати стилі</key>
    <key alias="tableInsert">Вставити таблицю</key>
    <key alias="undo">Скасувати</key>
  </area>
  <area alias="colorpicker">
    <key alias="noColors">Ви не вказали жодного допустимого кольору</key>
  </area>
  <area alias="colors">
    <key alias="blue">Синій</key>
  </area>
  <area alias="content">
    <key alias="about">Про цю сторінку</key>
    <key alias="alias">Аліас</key>
    <key alias="alternativeTextHelp">(як би Ви описали зображення по телефону)</key>
    <key alias="alternativeUrls">Альтернативні посилання</key>
    <key alias="altTextOptional">Альтернативний текст (необов'язково)</key>
    <key alias="childItems" version="7.0">Елементи списку</key>
    <key alias="clickToEdit">Натисніть для редагування цього елемента</key>
    <key alias="contentRoot">Початковий вузол з вмістом</key>
    <key alias="createBy">Створено користувачем</key>
    <key alias="createByDesc" version="7.0">Початковий автор</key>
    <key alias="createDate">Дата створення</key>
    <key alias="createDateDesc" version="7.0">Дата/час створення документа</key>
    <key alias="documentType">Тип документа</key>
    <key alias="editing">Редагування</key>
    <key alias="expireDate">Приховати</key>
    <key alias="getUrlException">УВАГА: неможливо отримати URL-адресу документа (внутрішня помилка - подробиці в системному журналі)</key>
    <key alias="isPublished" version="7.2">Опубліковано</key>
    <key alias="isSensitiveValue">Це значення приховано. Якщо Вам потрібний доступ до цього значення, зв'яжіться з адміністратором веб-сайту.</key>
    <key alias="isSensitiveValue_short">Це значення приховано.</key>
    <key alias="itemChanged">Цей документ було змінено після публікації</key>
    <key alias="itemNotPublished">Цей документ не опубліковано</key>
    <key alias="lastPublished">Документ опубліковано</key>
    <key alias="noItemsToShow">Немає елементів.</key>
    <key alias="listViewNoItems" version="7.1.5">У цьому списку поки що немає елементів.</key>
    <key alias="listViewNoContent">Вміст поки що не додано</key>
    <key alias="listViewNoMembers">Учасники поки що не додані</key>
    <key alias="mediaLinks">Посилання на медіа-елементи</key>
    <key alias="mediatype">Тип медіа-контенту</key>
    <key alias="membergroup">Група учасників</key>
    <key alias="memberof">Включено в групу(и)</key>
    <key alias="memberrole">Роль учасника</key>
    <key alias="membertype">Тип учасника</key>
    <key alias="nestedContentDeleteItem">Ви впевнені, що хочете видалити цей елемент?</key>
    <key alias="nestedContentEditorNotSupported">Властивість '%0%' використовує редактор '%1%', який не підтримується для вкладеного вмісту.</key>
    <key alias="noChanges">Не було зроблено жодних змін</key>
    <key alias="noDate">Дата не вказана</key>
    <key alias="nodeName">Заголовок сторінки</key>
    <key alias="noMediaLink">Цей медіа-елемент не містить посилання</key>
    <key alias="notmemberof">Доступні групи</key>
    <key alias="otherElements">Властивості</key>
    <key alias="parentNotPublished">Цей документ опубліковано, але приховано, тому що його батьківський документ '%0%' не опублікований</key>
    <key alias="parentNotPublishedAnomaly">УВАГА: цей документ опубліковано, але його немає у глобальному кеші (внутрішня помилка - подробиці в системному журналі)</key>
    <key alias="publish">Опублікувати</key>
    <key alias="published">Опубліковано</key>
    <key alias="publishedPendingChanges">Опубліковано (є зміни)</key>
    <key alias="publishStatus">Стан публікації</key>
    <key alias="releaseDate">Опублікувати</key>
    <key alias="removeDate">Очистити дату</key>
    <key alias="routeError">УВАГА: цей документ опубліковано, але його URL такий самий як в документа %0%</key>
    <key alias="scheduledPublishServerTime">Цей час буде відповідати наступному часу на сервері:</key>
    <key alias="scheduledPublishDocumentation"><![CDATA[<a href="https://docs.umbraco.com/umbraco-cms/fundamentals/data/scheduled-publishing#timezones" target="_blank" rel="noopener">Що це означає?</a>]]></key>
    <key alias="setDate">Задати дату</key>
    <key alias="sortDone">Порядок сортування оновлено</key>
    <key alias="sortHelp">Для сортування вузлів просто перетягуйте вузли або натисніть на заголовок стовпця. Ви можете вибрати кілька вузлів, утримуючи клавіші "shift" або "ctrl" при виборі</key>
    <key alias="statistics">Статистика</key>
    <key alias="target" version="7.0">Ціль</key>
    <key alias="titleOptional">Заголовок (необов'язково)</key>
    <key alias="type">Тип</key>
    <key alias="unpublish">Скасувати публікацію</key>
    <key alias="unpublished">Публікацію скасовано</key>
    <key alias="unpublishDate">Скасувати публікацію</key>
    <key alias="updateDate">Останнє редагування</key>
    <key alias="updateDateDesc" version="7.0">Дата/час редагування документа</key>
    <key alias="updatedBy" version="7.0">Оновлено</key>
    <key alias="uploadClear">Видалити файл</key>
    <key alias="urls">Посилання на документ</key>
    <key alias="addTextBox">Додати нове текстове поле</key>
    <key alias="removeTextBox">Видалити це текстове поле</key>
  </area>
  <area alias="contentPicker">
    <key alias="pickedTrashedItem">Вибрано елемент вмісту, який вилучено або знаходиться в корзині.</key>
    <key alias="pickedTrashedItems">Вибрані елементи вмісту, які в даний час видалені або знаходяться в корзині</key>
  </area>
  <area alias="contentTypeEditor">
    <key alias="compositions">Композиції</key>
    <key alias="noGroups">Ви не додали жодної вкладки</key>
    <key alias="inheritedFrom">Успадковано від</key>
    <key alias="addProperty">Додати властивість</key>
    <key alias="requiredLabel">Обов'язкова позначка</key>
    <key alias="enableListViewHeading">Відображення у форматі списку</key>
    <key alias="enableListViewDescription">Встановлює подання документа даного типу у вигляді сортованого списку дочірніх документів з функцією пошуку, на відміну від звичайного подання дочірніх документів у вигляді дерева</key>
    <key alias="allowedTemplatesHeading">Допустимі шаблони</key>
    <key alias="allowedTemplatesDescription">Виберіть список допустимих шаблонів для зіставлення документів цього типу</key>
    <key alias="allowAsRootHeading">Дозволити як кореневий елемента</key>
    <key alias="allowAsRootDescription">Дозволяє створювати документи цього типу у самому корені дерева вмісту</key>
    <key alias="childNodesHeading">Допустимі типи дочірніх документів</key>
    <key alias="childNodesDescription">Дозволяє вказати перелік типів документів, допустимих для створення документів, дочірніх до цього типу</key>
    <key alias="chooseChildNode">Вибрати дочірній вузол</key>
    <key alias="compositionsDescription">Успадкувати вкладки та властивості з існуючого типу документів. Вкладки будуть або додані до створюваного типу, або у разі збігу назв вкладок будуть додані успадковані властивості.</key>
    <key alias="compositionInUse">Цей тип документів вже бере участь у композиції іншого типу, тому сам може бути композицією.</key>
    <key alias="compositionUsageHeading">Де використовується ця композиція?</key>
    <key alias="compositionUsageSpecification">Ця композиція зараз використовується при створенні таких типів документів:</key>
    <key alias="noAvailableCompositions">Наразі немає типів документів, допустимих побудови композиції.</key>
    <key alias="availableEditors">Доступні редактори</key>
    <key alias="reuse">Перевикористати</key>
    <key alias="editorSettings">Налаштування редактора</key>
    <key alias="configuration">Конфігурування</key>
    <key alias="yesDelete">ТАК, видалити</key>
    <key alias="movedUnderneath">переміщені всередину</key>
    <key alias="copiedUnderneath">скопійовані всередину</key>
    <key alias="folderToMove">Вибрати папку для переміщення</key>
    <key alias="folderToCopy">Вибрати папку для копіювання</key>
    <key alias="structureBelow">у структурі дерева</key>
    <key alias="allDocumentTypes">Всі типи документів</key>
    <key alias="allDocuments">Всі документи</key>
    <key alias="allMediaItems">Всі медіа-елементи</key>
    <key alias="usingThisDocument">, що використовують цей тип документів, будуть безповоротно видалені, будь ласка, підтвердьте цю дію.</key>
    <key alias="usingThisMedia">, що використовують цей тип медіа, будуть безповоротно видалені, будь ласка, підтвердьте цю дію.</key>
    <key alias="usingThisMember">, що використовують цей тип учасників, будуть безповоротно видалені, будь ласка, підтвердьте цю дію.</key>
    <key alias="andAllDocuments">і всі документи, які використовують цей тип</key>
    <key alias="andAllMediaItems">і всі медіа-елементи, які використовують цей тип</key>
    <key alias="andAllMembers">і всі учасники, які використовують цей тип</key>
    <key alias="memberCanEdit">Учасник може змінити</key>
    <key alias="memberCanEditDescription">Дозволяє редагування значення цієї властивості учасником у своєму профілі</key>
    <key alias="isSensitiveData">Конфіденційні дані</key>
    <key alias="isSensitiveDataDescription">Приховує значення цієї властивості від редакторів вмісту, які не мають доступу до конфіденційної інформації</key>
    <key alias="showOnMemberProfile">Показати у профілі користувача</key>
    <key alias="showOnMemberProfileDescription">Дозволяє показ цієї властивості у профілі учасника</key>
    <key alias="tabHasNoSortOrder">для вкладки не вказано порядок сортування</key>
  </area>
  <area alias="create">
    <key alias="chooseNode">Де ви хочете створити новий %0%</key>
    <key alias="createContentBlueprint">Виберіть тип документів, для якого потрібно створити шаблон вмісту</key>
    <key alias="createUnder">Створити у вузлі</key>
    <key alias="newFolder">Нова папка</key>
    <key alias="newDataType">Новий тип даних</key>
    <key alias="noDocumentTypes" version="7.0"><![CDATA[Немає жодного дозволеного типу документів для створення. Необхідно дозволити потрібні Вам типи у секції "Налаштування" в дереві <strong>"Типи документів"</strong>.]]></key>
    <key alias="noMediaTypes" version="7.0"><![CDATA[Немає жодного дозволеного типу медіа-матеріалів для створення. Необхідно дозволити потрібні Вам типи у секції "Налаштування" в дереві <strong>"Типы медіа-матеріалів"</strong>.]]></key>
    <key alias="updateData">Виберіть тип та заголовок</key>
    <key alias="documentTypeWithoutTemplate">Тип документа без зіставленого шаблону</key>
    <key alias="newJavascriptFile">Новий файл javascript</key>
    <key alias="newEmptyPartialView">Нове порожнє часткове представлення</key>
    <key alias="newPartialViewMacro">Новий макрос-представлення</key>
    <key alias="newPartialViewFromSnippet">Нове часткове представлення за зразком (сніпетом)</key>
    <key alias="newPartialViewMacroFromSnippet">Нове макрос-представлення за зразком (сніпетом)</key>
    <key alias="newPartialViewMacroNoMacro">Нове макрос-представлення (без реєстрації макроса)</key>
  </area>
  <area alias="dashboard">
    <key alias="browser">Огляд сайту</key>
    <key alias="dontShowAgain">- Приховати - </key>
    <key alias="nothinghappens">Якщо адміністративна панель не завантажується, Вам, можливо, слід дозволити спливаючі вікна даного сайту</key>
    <key alias="openinnew">було відкрито у новому вікні</key>
    <key alias="restart">Перезапустити</key>
    <key alias="visit">Відвідати</key>
    <key alias="welcome">Вітаємо</key>
  </area>
  <area alias="defaultdialogs">
    <key alias="anchorInsert">Назва</key>
    <key alias="assignDomain">Управління доменами</key>
    <key alias="closeThisWindow">Закрити це вікно</key>
    <key alias="confirmdelete">Ви впевнені, що хочете видалити</key>
    <key alias="confirmdisable">Ви впевнені, що хочете заборонити</key>
    <key alias="confirmlogout">Ви впевнені?</key>
    <key alias="confirmSure">Ви впевнені?</key>
    <key alias="cut">Вирізати</key>
    <key alias="editdictionary">Редагувати статтю словника</key>
    <key alias="editlanguage">Змінити мову</key>
    <key alias="insertAnchor">Вставити локальне посилання (якір)</key>
    <key alias="insertCharacter">Вставити символ</key>
    <key alias="insertgraphicheadline">Вставити графічний заголовок</key>
    <key alias="insertimage">Вставити зображення</key>
    <key alias="insertlink">Вставити посилання</key>
    <key alias="insertMacro">Вставити макрос</key>
    <key alias="inserttable">Вставити таблицю</key>
    <key alias="lastEdited">Остання зміна</key>
    <key alias="link">Посилання</key>
    <key alias="linkinternal">Внутрішнє посилання:</key>
    <key alias="linklocaltip">Для визначення локального посилання, використовуйте "#" першим символом</key>
    <key alias="linknewwindow">Відкрити у новому вікні?</key>
    <key alias="macroDoesNotHaveProperties">Цей макрос не має властивостей, що редагуються.</key>
    <key alias="nodeNameLinkPicker">Заголовок посилання</key>
    <key alias="noIconsFound">Жодної піктограми не знайдено</key>
    <key alias="paste">Вставити</key>
    <key alias="permissionsEdit">Змінити дозволи для</key>
    <key alias="permissionsSet">Встановити дозволи для</key>
    <key alias="permissionsSetForGroup">Встановити права доступу до '%0%' для групи користувачів '%1%'</key>
    <key alias="permissionsHelp">Виберіть групу (и) користувачів, для яких потрібно встановити дозвіл</key>
    <key alias="recycleBinDeleting">Всі елементи у кошику зараз видаляються. Будь ласка, не закривайте це вікно до закінчення процесу видалення</key>
    <key alias="recycleBinIsEmpty">Корзина порожня</key>
    <key alias="recycleBinWarning">Ви більше не зможете відновити елементи, видалені з кошика</key>
    <key alias="regexSearchError"><![CDATA[Сервіс <a target='_blank' rel='noopener' href='http://regexlib.com'>regexlib.com</a> недоступний, це незалежить від нас. Просимо вибачити за завдані незручності.]]></key>
    <key alias="regexSearchHelp">Використовуйте пошук регулярних виразів, щоб додати сервіс перевірки до поля Вашої форми. Наприклад: 'email, 'zip-code', 'URL'</key>
    <key alias="removeMacro">Видалити макрос</key>
    <key alias="requiredField">Обов'язкове поле</key>
    <key alias="sitereindexed">Сайт переіндексований</key>
    <key alias="siterepublished">Кеш сайту було оновлено. Весь опублікований вміст приведено в актуальний стан, у той час як неопублікований вміст, як і раніше, не опубліковано</key>
    <key alias="siterepublishHelp">Кеш сайту буде повністю оновлено. Весь опублікований вміст буде оновлено, тоді як неопублікований вміст, як і раніше, залишиться неопублікованим.</key>
    <key alias="tableColumns">Кількість стовпців</key>
    <key alias="tableRows">Кількість рядків</key>
    <key alias="thumbnailimageclickfororiginal">Клацніть на зображенні, щоб побачити повнорозмірну версію</key>
    <key alias="treepicker">Виберіть елемент</key>
    <key alias="urlLinkPicker">Посилання</key>
    <key alias="viewCacheItem">Перегляд елемента кешу</key>
    <key alias="relateToOriginalLabel">Зв'язати із оригіналом</key>
    <key alias="includeDescendants">Включаючи всі дочірні</key>
    <key alias="theFriendliestCommunity">Найдружелюбніша спільнота</key>
    <key alias="linkToPage">Посилання на сторінку</key>
    <key alias="openInNewWindow">Відкривати посилання у новому вікні або вкладці браузера</key>
    <key alias="linkToMedia">Посилання на медіа-елемент</key>
    <key alias="selectMedia">Вибрати медіа</key>
    <key alias="selectMediaStartNode">Вибрати початковий вузол медіа-бібліотеки</key>
    <key alias="selectIcon">Вибрати піктограму</key>
    <key alias="selectItem">Вибрати елемент</key>
    <key alias="selectLink">Вибрати посилання</key>
    <key alias="selectMacro">Вибрати макрос</key>
    <key alias="selectContent">Вибрати вміст</key>
    <key alias="selectContentStartNode">Вибрати початковий вузол вмісту</key>
    <key alias="selectMember">Вибрати учасника</key>
    <key alias="selectMemberGroup">Вибрати групу учасників</key>
    <key alias="selectNode">Вибрати вузол</key>
    <key alias="selectSections">Вибрати розділи</key>
    <key alias="selectUsers">Вибрати користувачів</key>
    <key alias="noMacroParams">Це макрос без параметрів</key>
    <key alias="noMacros">Немає макросів, доступних для вставки в редактор</key>
    <key alias="externalLoginProviders">Провайдери автентифікації</key>
    <key alias="exceptionDetail">Детальне повідомлення про помилку</key>
    <key alias="stacktrace">Трасування стеку</key>
    <key alias="innerException">Внутрішня помилка</key>
    <key alias="linkYour">Зв'язати</key>
    <key alias="unLinkYour">Розірвати зв'язок</key>
    <key alias="account">облікового запису</key>
    <key alias="selectEditor">Вибрати редактор</key>
    <key alias="selectSnippet">Вибрати зразок</key>
  </area>
  <area alias="dictionaryItem">
    <key alias="description"><![CDATA[
		Ниже Ви можете вказати різні переклади даної статті словника '<em>%0%</em>'<br/>Додати інші мови можна, скориставшись пунктом 'Мови' в меню зліва
		]]></key>
    <key alias="displayName">Назва мови (культури)</key>
    <key alias="changeKeyError"><![CDATA[
      Ключ '%0%' вже існує у словнику.
   ]]></key>
    <key alias="overviewTitle">Огляд словника</key>
  </area>
  <area alias="editcontenttype">
    <key alias="createListView" version="7.2">Створити список користувача</key>
    <key alias="removeListView" version="7.2">Видалити список користувача</key>
  </area>
  <area alias="editdatatype">
    <key alias="addPrevalue">Додати попередньо встановлене значення</key>
    <key alias="dataBaseDatatype">Тип даних у БД</key>
    <key alias="guid">GUID типу даних</key>
    <key alias="renderControl">Редактор властивості</key>
    <key alias="rteButtons">Кнопки</key>
    <key alias="rteEnableAdvancedSettings">Увімкнути розширені налаштування для</key>
    <key alias="rteEnableContextMenu">Увімкнути контекстне меню</key>
    <key alias="rteMaximumDefaultImgSize">Максимальний розмір для зображень за замовчуванням</key>
    <key alias="rteRelatedStylesheets">Зіставлені стилі CSS</key>
    <key alias="rteShowLabel">Показати мітку</key>
    <key alias="rteWidthAndHeight">Ширина та висота</key>
    <key alias="selectFolder">Виберіть папку, щоб перемістити до неї</key>
    <key alias="inTheTree">у структурі дерева нижче</key>
    <key alias="wasMoved">був переміщений до папки</key>
  </area>
  <area alias="emptyStates">
    <key alias="emptyDictionaryTree">Немає доступних елементів словника</key>
  </area>
  <area alias="errorHandling">
    <key alias="errorButDataWasSaved">Ваші дані збережені, але для того, щоб опублікувати цей документ, Ви повинні спочатку виправити такі помилки:</key>
    <key alias="errorExistsWithoutTab">%0% вже існує</key>
    <key alias="errorHeader">Виявлено такі помилки:</key>
    <key alias="errorHeaderWithoutTab">Виявлено такі помилки:</key>
    <key alias="errorInPasswordFormat">Пароль повинен складатися як мінімум з %0% символів, хоча б %1% з яких не є літерами</key>
    <key alias="errorIntegerWithoutTab">%0% має бути цілочисельним значенням</key>
    <key alias="errorMandatory">%0% в %1% є обов'язковим полем</key>
    <key alias="errorMandatoryWithoutTab">%0% є обов'язковим полем</key>
    <key alias="errorRegExp">%0% в %1%: дані у некоректному форматі</key>
    <key alias="errorRegExpWithoutTab">%0% - дані у некоректному форматі</key>
  </area>
  <area alias="errors">
    <key alias="receivedErrorFromServer">Отримано повідомлення про помилку від сервера</key>
    <key alias="codemirroriewarning">ПРЕДУПРЕЖДЕНИЕ! Незважаючи на те, що CodeMirror за замовчуванням дозволено в цій конфігурації, він, як і раніше, відключений для браузерів Internet Explorer через нестабільну роботу</key>
    <key alias="contentTypeAliasAndNameNotNull">Вкажіть, будь ласка, аліас та ім'я для цієї властивості!</key>
    <key alias="dissallowedMediaType">Використання даного типу файлів на сайті заборонено адміністратором</key>
    <key alias="filePermissionsError">Помилка доступу до вказаного файлу або папки</key>
    <key alias="macroErrorLoadingPartialView">Помилка завантаження коду у частковому преставленні (файл: %0%)</key>
    <key alias="missingTitle">Вкажіть заголовок</key>
    <key alias="missingType">Виберіть тип</key>
    <key alias="pictureResizeBiggerThanOrg">Ви намагаєтеся збільшити зображення порівняно з його вихідним розміром. Впевнені, що хочете це зробити?</key>
    <key alias="startNodeDoesNotExists">Початковий вузол було видалено, зв'яжіться з Вашим адміністратором</key>
    <key alias="stylesMustMarkBeforeSelect">Для зміни стилю відзначте фрагмент тексту</key>
    <key alias="stylesNoStylesOnPage">Не визначено жодного доступного стилю</key>
    <key alias="tableColMergeLeft">Помістіть курсор у ліву з двох комірок, які хочете об'єднати</key>
    <key alias="tableSplitNotSplittable">Не можна розділити комірку, яка не була до цього об'єднана</key>
  </area>
  <area alias="general">
    <key alias="about">Про систему</key>
    <key alias="action">Дія</key>
    <key alias="actions">Дії</key>
    <key alias="add">Додати</key>
    <key alias="alias">Аліас</key>
    <key alias="all">Все</key>
    <key alias="areyousure">Ви впевнені?</key>
    <key alias="back">Назад</key>
    <key alias="border">Межі</key>
    <key alias="by">користувачем</key>
    <key alias="cancel">Відміна</key>
    <key alias="cellMargin">Відступ комірки</key>
    <key alias="choose">Вибрати</key>
    <key alias="close">Закрити</key>
    <key alias="closewindow">Закрити вікно</key>
    <key alias="comment">Примітка</key>
    <key alias="confirm">Підтвердити</key>
    <key alias="constrain">Зберігати пропорції</key>
    <key alias="constrainProportions">Зберігати пропорції</key>
    <key alias="continue">Далі</key>
    <key alias="copy">Копіювати</key>
    <key alias="create">Створити</key>
    <key alias="database">База даних</key>
    <key alias="date">Дата</key>
    <key alias="default">За замовчуванням</key>
    <key alias="delete">Видалити</key>
    <key alias="deleted">Видалено</key>
    <key alias="deleting">Видаляється...</key>
    <key alias="design">Дизайн</key>
    <key alias="dictionary">Словник</key>
    <key alias="dimensions">Розміри</key>
    <key alias="down">Вниз</key>
    <key alias="download">Завантажити</key>
    <key alias="edit">Змінити</key>
    <key alias="edited">Змінено</key>
    <key alias="elements">Елементи</key>
    <key alias="email">Email адреса</key>
    <key alias="error">Помилка</key>
    <key alias="findDocument">Знайти</key>
    <key alias="first">Початок</key>
    <key alias="general">Загальне</key>
    <key alias="groups">Групи</key>
    <key alias="folder">Папка</key>
    <key alias="height">Висота</key>
    <key alias="help">Довідка</key>
    <key alias="hide">Приховати</key>
    <key alias="history">Історія</key>
    <key alias="icon">Піктограма</key>
    <key alias="import">Імпорт</key>
    <key alias="info">Інфо</key>
    <key alias="innerMargin">Внутрішній відступ</key>
    <key alias="insert">Вставити</key>
    <key alias="install">Встановити</key>
    <key alias="invalid">Невірно</key>
    <key alias="justify">Вирівнювання</key>
    <key alias="label">Назва</key>
    <key alias="language">Мова</key>
    <key alias="last">Кінець</key>
    <key alias="layout">Макет</key>
    <key alias="links">Посилання</key>
    <key alias="loading">Завантаження</key>
    <key alias="locked">БЛОКУВАННЯ</key>
    <key alias="login">Увійти</key>
    <key alias="logoff">Вийти</key>
    <key alias="logout">Вихід</key>
    <key alias="macro">Макрос</key>
    <key alias="mandatory">Обов'язково</key>
    <key alias="message">Повідомлення</key>
    <key alias="move">Перемістити</key>
    <key alias="name">Назва</key>
    <key alias="new">Новий</key>
    <key alias="next">Наст.</key>
    <key alias="no">Ні</key>
    <key alias="noItemsInList">Тут поки що немає елементів</key>
    <key alias="of">з</key>
    <key alias="off">Вимк</key>
    <key alias="ok">Ok</key>
    <key alias="open">Відкрити</key>
    <key alias="on">Вкл</key>
    <key alias="options">Варіанти</key>
    <key alias="or">або</key>
    <key alias="orderBy">Сортування за</key>
    <key alias="password">Пароль</key>
    <key alias="path">Шлях</key>
    <key alias="pleasewait">Хвилинку...</key>
    <key alias="previous">Попер.</key>
    <key alias="properties">Властивості</key>
    <key alias="reciept">Email адреса для отримання даних</key>
    <key alias="recycleBin">Корзина</key>
    <key alias="recycleBinEmpty">Ваша корзина порожня</key>
    <key alias="remaining">Залишилось</key>
    <key alias="remove">Видалити</key>
    <key alias="rename">Перейменувати</key>
    <key alias="renew">Оновити</key>
    <key alias="required" version="7.0">Обов'язкове</key>
    <key alias="retrieve">Отримати</key>
    <key alias="retry">Повторити</key>
    <key alias="rights">Дозволи</key>
    <key alias="scheduledPublishing">Публікація за розкладом</key>
    <key alias="search">Пошук</key>
    <key alias="searchNoResult">На жаль, нічого не знайшлося</key>
    <key alias="searchResults">Результати пошуку</key>
    <key alias="server">Сервер</key>
    <key alias="show">Показати</key>
    <key alias="showPageOnSend">Показати сторінку при надсиланні</key>
    <key alias="size">Розмір</key>
    <key alias="sort">Сортувати</key>
    <key alias="status">Стан</key>
    <key alias="submit">Відправити</key>
    <key alias="type">Тип</key>
    <key alias="typeToSearch">Що шукати?</key>
    <key alias="up">Вгору</key>
    <key alias="update">Оновити</key>
    <key alias="upgrade">Оновлення</key>
    <key alias="upload">Завантажити</key>
    <key alias="url">Інтернет-посилання</key>
    <key alias="user">Користувач</key>
    <key alias="username">Ім'я користувача</key>
    <key alias="value">Значення</key>
    <key alias="view">Перегляд</key>
    <key alias="welcome">Ласкаво просимо...</key>
    <key alias="width">Ширина</key>
    <key alias="yes">Так</key>
    <key alias="reorder">Пересортувати</key>
    <key alias="reorderDone">Пересортування завершено</key>
    <key alias="preview">Попередній перегляд</key>
    <key alias="changePassword">Змінити пароль</key>
    <key alias="to">до</key>
    <key alias="listView">Список</key>
    <key alias="saving">Збереження...</key>
    <key alias="current">поточний</key>
    <key alias="selected">вибрано</key>
    <key alias="embed">Вбудувати</key>
  </area>
  <area alias="graphicheadline">
    <key alias="backgroundcolor">Колір фону</key>
    <key alias="bold">Напівжирний</key>
    <key alias="color">Колір тексту</key>
    <key alias="font">Шрифт</key>
    <key alias="text">Текст</key>
  </area>
  <area alias="grid">
    <key alias="media">Зображення</key>
    <key alias="macro">Макрос</key>
    <key alias="addElement">Додати вміст</key>
    <key alias="dropElement">Скинути вміст</key>
    <key alias="addGridLayout">Додати шаблон сітки</key>
    <key alias="addGridLayoutDetail">Налаштуйте шаблон, задаючи ширину колонок або додаючи додаткові розділи</key>
    <key alias="addRowConfiguration">Додати конфігурацію рядка</key>
    <key alias="addRowConfigurationDetail">Налаштуйте рядок, задаючи ширину комірок або додаючи додаткові комірки</key>
    <key alias="addRows">Додати нові рядки</key>
    <key alias="allowAllEditors">Доступні всі редактори</key>
    <key alias="allowAllRowConfigurations">Доступні всі конфігурації рядків</key>
    <key alias="chooseLayout">Виберіть шаблон</key>
    <key alias="clickToEmbed">Клацніть для вбудовування</key>
    <key alias="clickToInsertImage">Натисніть, щоб вставити зображення</key>
    <key alias="columns">Колонки</key>
    <key alias="contentNotAllowed">Неприпустимий тип вмісту</key>
    <key alias="contentAllowed">Даний тип вмісту дозволено</key>
    <key alias="columnsDetails">Сумарна кількість колонок у шаблоні сітки</key>
    <key alias="gridLayouts">Шаблони сітки</key>
    <key alias="gridLayoutsDetail">Шаблони є робочим простором для редактора сітки, зазвичай Вам знадобиться не більше одного або двох шаблонів.</key>
    <key alias="insertControl">Вставити елемент</key>
    <key alias="placeholderWriteHere">Напишіть...</key>
    <key alias="rowConfigurations">Конфігурації рядків</key>
    <key alias="rowConfigurationsDetail">Рядки - це послідовності комірок з горизонтальним розташуванням</key>
    <key alias="settings">Налаштування</key>
    <key alias="settingsApplied">Налаштування застосовані</key>
    <key alias="settingsDetails">Вкажіть налаштування, доступні редакторам для зміни</key>
    <key alias="styles">Стилі</key>
    <key alias="stylesDetails">Вкажіть стилі, доступні редакторам для зміни</key>
    <key alias="setAsDefault">Встановити за замовчуванням</key>
    <key alias="chooseExtra">Вибрати додатково</key>
    <key alias="chooseDefault">Вибрати за замовчуванням</key>
    <key alias="areAdded">додані</key>
    <key alias="maxItemsDescription">Залиште порожнім або задайте 0 для зняття ліміту</key>
    <key alias="maxItems">Максимальна кількість</key>
  </area>
  <area alias="headers">
    <key alias="page">Сторінка</key>
  </area>
  <area alias="healthcheck">
    <!-- The following keys get these tokens passed in:
	     0: Current value
		   1: Recommended value
		   2: XPath
		   3: Configuration file path
	  -->
    <key alias="checkSuccessMessage">Для параметра встановлено рекомендоване значення: '%0%'.</key>
    <key alias="checkErrorMessageDifferentExpectedValue">Очікуване значення '%1%' для параметра '%2%' в файлі конфігурації '%3%', знайдене значення: '%0%'.</key>
    <key alias="checkErrorMessageUnexpectedValue">Знайдено неочікуване значення '%0%' для параметра '%2%' в файлі конфігурації '%3%'.</key>
    <!-- The following keys get these tokens passed in:
	     0: Current value
		   1: Recommended value
	  -->
    <key alias="macroErrorModeCheckSuccessMessage">Параметр 'MacroErrors' встановлений в '%0%'.</key>
    <key alias="macroErrorModeCheckErrorMessage">Параметр 'MacroErrors' встановлений в '%0%', що може спричинити неповну обробку частини сторінок або всіх сторінок сайту за наявності помилок у макросах. Усунути це можна шляхом встановлення значення в '%1%'.</key>
    <!-- The following keys get these tokens passed in:
	     0: Current value
		   1: Recommended value
		   2: Server version
	  -->
    <!-- The following keys get predefined tokens passed in that are not all the same, like above -->
    <key alias="httpsCheckValidCertificate">Сертифікат вашого веб-сайту відзначений як перевірений.</key>
    <key alias="httpsCheckInvalidCertificate">Помилка перевірки сертифіката: '%0%'</key>
    <key alias="httpsCheckIsCurrentSchemeHttps">Зараз Ви %0% переглядаєте сайт, використовуючи протокол HTTPS.</key>
    <!-- The following keys don't get tokens passed in -->
    <key alias="compilationDebugCheckSuccessMessage">Режим компіляції з налагодженням вимкнено.</key>
    <key alias="compilationDebugCheckErrorMessage">Режим компіляції з налагодженням зараз увімкнено. Перед розміщенням сайту в мережі рекомендується вимкнути.</key>
    <!-- The following keys get these tokens passed in:
	    0: Comma delimitted list of failed folder paths
  	-->
    <!-- The following keys get these tokens passed in:
	    0: Path to the file not found
  	-->
    <!-- The following keys get these tokens passed in:
	    0: Comma delimitted list of failed folder paths
  	-->
    <key alias="clickJackingCheckHeaderFound"><![CDATA[Заголовок або мета-тег <strong>X-Frame-Options</strong>, що використовується для управління можливістю розміщувати сайт у IFRAME на іншому сайті, знайдено.]]></key>
    <key alias="clickJackingCheckHeaderNotFound"><![CDATA[Заголовок або мета-тег <strong>X-Frame-Options</strong>, що використовується для управління можливістю розміщувати сайт у IFRAME на іншому сайті, не знайдено.]]></key>
    <key alias="noSniffCheckHeaderFound"><![CDATA[Заголовок або мета-тег <strong>X-Content-Type-Options</strong>, що використовується для захисту від MIME-уязвимостей, знайдено.]]></key>
    <key alias="noSniffCheckHeaderNotFound"><![CDATA[Заголовок або мета-тег <strong>X-Content-Type-Options</strong>, що використовується для захисту від MIME-уязвимостей, не знайдено.]]></key>
    <key alias="hSTSCheckHeaderFound"><![CDATA[Заголовок <strong>Strict-Transport-Security</strong>, відомий також як HSTS-header, знайдено.]]></key>
    <key alias="hSTSCheckHeaderNotFound"><![CDATA[Заголовок <strong>Strict-Transport-Security</strong> не знайдено.]]></key>
    <key alias="xssProtectionCheckHeaderFound"><![CDATA[Заголовок <strong>X-XSS-Protection</strong> знайдено.]]></key>
    <key alias="xssProtectionCheckHeaderNotFound"><![CDATA[Заголовок <strong>X-XSS-Protection</strong> не знайдено.]]></key>
    <!-- The following key get these tokens passed in:
	    0: Comma delimitted list of headers found
  	-->
    <key alias="excessiveHeadersFound"><![CDATA[Виявлено наступні заголовки, що дозволяють з'ясувати базову технологію сайту: <strong>%0%</strong>.]]></key>
    <key alias="excessiveHeadersNotFound">Заголовки, які дають змогу з'ясувати базову технологію сайту, не виявлено.</key>
    <key alias="smtpMailSettingsConnectionSuccess">Параметри надсилання електронної пошти (SMTP) налаштовані коректно, сервіс працює як очікується.</key>
    <key alias="notificationEmailsCheckSuccessMessage"><![CDATA[Адреса для надсилання повідомлень є наступною: <strong>%0%</strong>.]]></key>
    <key alias="notificationEmailsCheckErrorMessage"><![CDATA[Адреса для надсилання повідомлень все ще встановлена за замовчуванням <strong>%0%</strong>.]]></key>
    <key alias="scheduledHealthCheckEmailBody"><![CDATA[<html><body><p>Зафіксовано такі результати автоматичної перевірки стану Umbraco за розкладом, запущеним %0% в %1%:</p>%2%</body></html>]]></key>
    <key alias="scheduledHealthCheckEmailSubject">Результат перевірки стану Umbraco: %0%</key>
  </area>
  <area alias="help">
    <key alias="theBestUmbracoVideoTutorials">Найкращі навчальні відео-курси з Umbraco</key>
  </area>
  <area alias="imagecropper">
    <key alias="reset">Скинути</key>
  </area>
  <area alias="installer">
    <key alias="databaseErrorCannotConnect">Програма інсталяції не може встановити підключення до бази даних.</key>
    <key alias="databaseFound">База даних виявлена та ідентифікована як</key>
    <key alias="databaseHeader">Конфігурація бази даних</key>
    <key alias="databaseInstall"><![CDATA[
		Натисніть кнопку <strong>Встановити</strong> щоб встановити базу даних Umbraco %0%
		]]></key>
    <key alias="databaseInstallDone"><![CDATA[Ваша База даних налаштована для роботи Umbraco %0%. Натисніть кнопку <strong>Далі</strong> для продовження.]]></key>
    <key alias="databaseText"><![CDATA[Для завершення цього кроку Вам потрібно мати деяку інформацію про Ваш сервер бази даних
		(рядок підключення "connection string")<br />
		Будь ласка, зв'яжіться з Вашим хостинг-провайдером, якщо є необхідність, а якщо встановлюєте на локальну робочу станцію або сервер, отримайте інформацію у Вашого системного адміністратора.]]></key>
    <key alias="databaseUpgrade"><![CDATA[
		<p>Натисніть кнопку <strong>Оновлення</strong>
		для того, щоб привести Вашу базу даних
		у відповідність до версії Umbraco %0%</p>
		<p>Будь ласка, не хвилюйтеся, жодного рядка Вашої бази даних
		не буде втрачено при цій операції, і після її завершення все буде працювати!</p>
		]]></key>
    <key alias="databaseUpgradeDone"><![CDATA[Вашу базу даних успішно оновлено до останньої версії %0%.<br/>Натисніть <strong>Далі</strong> для продовження. ]]></key>
    <key alias="databaseUpToDate"><![CDATA[Вказана Вами база даних знаходиться в актуальному стані. Натисніть кнопку <strong>Далі</strong> для продовження роботи майстра налаштувань]]></key>
    <key alias="defaultUserChangePass"><![CDATA[<strong>Пароль користувача за замовчуванням необхідно змінити!</strong>]]></key>
    <key alias="defaultUserDisabled"><![CDATA[<strong>Користувач за замовчуванням заблокований або не має доступу до Umbraco!</strong></p><p>Не буде вжито жодних подальших дій. Натисніть кнопку <strong>Далі</strong> для продовження.]]></key>
    <key alias="defaultUserPassChanged"><![CDATA[<strong>Пароль користувача за умовчанням успішно змінено в процесі встановлення!</strong></p><p>Немає потреби у будь-яких подальших діях. Натисніть кнопку <strong>Далі</strong> для продовження.]]></key>
    <key alias="defaultUserPasswordChanged">Пароль змінений!</key>
    <key alias="greatStart">Для початкового огляду можливостей системи рекомендуємо переглянути відеоматеріали для ознайомлення</key>
    <key alias="None">Система не встановлена.</key>
    <key alias="permissionsAffectedFolders">Зачеплені файли та папки</key>
    <key alias="permissionsAffectedFoldersMoreInfo">Більш детально про встановлення дозволів для Umbraco розказано тут</key>
    <key alias="permissionsAffectedFoldersText">Вам слід встановити дозволи для облікового запису ASP.NET на модифікацію наступних файлів та папок</key>
    <key alias="permissionsAlmostPerfect"><![CDATA[<strong>Налаштування дозволів у Вашій системі майже повністю відповідають вимогам Umbraco!</strong>
		<br /><br />Ви маєте можливість запускати Umbraco без проблем, проте не зможете скористатися такою сильною стороною системи Umbraco, як встановлення додаткових пакетів розширень та доповнень.]]></key>
    <key alias="permissionsHowtoResolve">Як вирішити проблему</key>
    <key alias="permissionsHowtoResolveLink">Натисніть тут, щоб прочитати текстову версію документа</key>
    <key alias="permissionsHowtoResolveText"><![CDATA[Будь ласка, подивіться наш <strong>відео-матеріал</strong>, присвячений установці дозволів для файлів та папок в Umbraco або прочитайте текстову версію документа.]]></key>
    <key alias="permissionsMaybeAnIssue"><![CDATA[<strong>Налаштування дозволів у Вашій файловій системі можуть бути неправильними!</strong>
		<br /><br />Ви маєте можливість запускати Umbraco без проблем,
		однак не зможете скористатися такою сильною стороною системи Umbraco як встановлення додаткових пакетів розширень та доповнень.]]></key>
    <key alias="permissionsNotReady"><![CDATA[<strong>Установки дозволів у файловій системі не підходять для роботи Umbraco!</strong>
		<br /><br />Якщо Ви хочете продовжити роботу з Umbraco,
		Вам необхідно скоригувати налаштування дозволів.]]></key>
    <key alias="permissionsPerfect"><![CDATA[<strong>Налаштування дозволів у Вашій системі ідеальні!</strong>
		<br /><br />Ви маєте можливість працювати з Umbraco в повному обсязі, включаючи встановлення додаткових пакетів розширень і доповнень!]]></key>
    <key alias="permissionsResolveFolderIssues">Вирішення проблеми з папками</key>
    <key alias="permissionsResolveFolderIssuesLink">Скористайтеся цим посиланням для отримання більш детальної інформації про проблеми створення папок від імені облікового запису ASP.NET</key>
    <key alias="permissionsSettingUpPermissions">Встановлення дозволів на папки</key>
    <key alias="permissionsText"><![CDATA[
		Системі Umbraco необхідні права на читання та запис файлів до деяких папок, щоб зберігати в них такі матеріали як, наприклад, зображення або документи PDF.
		Також подібним чином система зберігає кешовані дані вашого сайту з метою підвищення його продуктивності.
		]]></key>
    <key alias="runwayFromScratch"><![CDATA[Я хочу почати з 'чистої сторінки']]></key>
    <key alias="runwayFromScratchText"><![CDATA[
		На даний момент Ваш сайт абсолютно порожній, що є найкращим варіантом для старту
		"з чистої сторінки", щоб почати створювати власні типи документів і шаблони.
		(<a href="https://umbraco.tv/documentation/videos/for-site-builders/foundation/document-types">Тут можна дізнатися про це детальніше</a>) Ви також можете відкласти установку "Runway" на більш пізній час. Перейдіть до розділу "Розробка" та виберіть пункт "Пакети".
		]]></key>
    <key alias="runwayHeader">Ви тільки що встановили чисту платформу Umbraco. Який крок буде наступним?</key>
    <key alias="runwayInstalled">"Runway" встановлений</key>
    <key alias="runwayInstalledText"><![CDATA[
		Базовий пакет системи встановлено. Виберіть, які модулі Ви хочете встановити поверх
		базового пакету.<br />Нижче наведено список модулів, рекомендованих до встановлення, змініть його за необхідності, або ознайомтеся з <a href="#" onclick="toggleModules(); return false;" id="toggleModuleList">повним списком модулів</a>
		]]></key>
    <key alias="runwayOnlyProUsers">Рекомендовано лише для досвідчених користувачів</key>
    <key alias="runwaySimpleSite">Я хочу почати з встановлення простого демонстраційного сайту</key>
    <key alias="runwaySimpleSiteText"><![CDATA[
		<p>"Runway" - це простий демонстраційний сайт, що надає базовий перелік шаблонів та типів документів.
		Програма установки може налаштувати "Runway" для Вас автоматично,
		але Ви можете надалі вільно змінювати, розширювати чи видалити його.
		Цей демонстраційний сайт не є необхідною частиною, і Ви можете вільно
		використовувати Umbraco без нього. Однак, "Runway" надає Вам можливість
		максимально швидко познайомитися з базовими принципами та технікою побудови сайтів
		на основі Umbraco. Якщо Ви оберете варіант із встановленням "Runway",
		Вам буде запропоновано вибір базових будівельних блоків (т.зв. модулів Runway) для розширення можливостей сторінок сайту Runway.</p>
		<small><em>В "Runway" входять:</em>"Домашня" (головна) сторінка, сторінка "Початок роботи",
		сторінка встановлення модулів.<br /> <em>Додаткові модулі:</em>Базова навігація, Карта сайту, Форма зворотнього зв'язку, Галерея.</small>
		]]></key>
    <key alias="runwayWhatIsRunway">Що таке "Runway"</key>
    <key alias="step1">Крок 1 з 5: Ліцензійна угода</key>
    <key alias="step2">Крок 2 з 5: конфігурація бази даних</key>
    <key alias="step3">Крок 3 з 5: перевірка файлових дозволів</key>
    <key alias="step4">Крок 4 з 5: перевірка безпеки</key>
    <key alias="step5">Крок 5 з 5: система готова для початку роботи</key>
    <key alias="thankYou">Дякуємо, що вибрали Umbraco</key>
    <key alias="theEndBrowseSite"><![CDATA[<h3>Огляд Вашого нового сайту</h3>Ви встановили "Runway",
		чому б не подивитися, як виглядає Ваш новий сайт?]]></key>
    <key alias="theEndFurtherHelp"><![CDATA[<h3>Подальше вивчення та допомога</h3>
		Отримуйте допомогу від нашої чудової спільноти користувачів, вивчайте документацію або переглядайте наші вільно розповсюджувані відео-матеріали про те, як створити власний нескладний сайт, використовувати розширення та пакети, а також короткий посібник з термінології Umbraco.]]></key>
    <key alias="theEndHeader">Система Umbraco %0% встановлена та готова до роботи</key>
    <key alias="theEndInstallSuccess"><![CDATA[Ви можете розпочати роботу <strong>прямо зараз</strong>,
		скориставшись посиланням "Почати роботу з Umbraco". <br />Якщо Ви <strong>новачок у світі Umbraco</strong>, Ви зможете знайти багато корисних посилань на ресурси на сторінці "Початок роботи".]]></key>
    <key alias="theEndOpenUmbraco"><![CDATA[<h3>Почніть роботу з Umbraco</h3>
		Для того, щоб почати адмініструвати свій сайт, просто відкрийте адміністративну панель Umbraco та почніть оновлювати контент, змінювати шаблони сторінок та стилі CSS або додавати нову функціональність]]></key>
    <key alias="Unavailable">Спроба з'єднання з базою даних зазнала невдачі.</key>
    <key alias="Version3">Версія Umbraco 3</key>
    <key alias="Version4">Версія Umbraco 4</key>
    <key alias="watch">Дивитись</key>
    <key alias="welcomeIntro"><![CDATA[Цей майстер проведе Вас через конфігураційний процес
		<strong>Umbraco %0%</strong> у формі "чистого" встановлення або оновлення попередньої версії 3.x.
		<br /><br />Натисніть кнопку <strong>"Далі"</strong> для початку роботи майстра.]]></key>
  </area>
  <area alias="language">
    <key alias="cultureCode">Код мови</key>
    <key alias="displayName">Назва мови</key>
  </area>
  <area alias="lockout">
    <key alias="lockoutWillOccur">Ви були відсутній деякий час. Було здійснено автоматичний вихід у</key>
    <key alias="renewSession">Оновіть зараз, щоб зберегти зроблені зміни</key>
  </area>
  <area alias="login">
    <key alias="bottomText"><![CDATA[<p style="text-align:right;">&copy; 2001 - %0% <br /><a href="https://umbraco.com" style="text-decoration: none" target="_blank" rel="noopener">umbraco.com</a></p>]]></key>
    <key alias="greeting0">Вітаємо</key>
    <key alias="greeting1">Вітаємо</key>
    <key alias="greeting2">Вітаємо</key>
    <key alias="greeting3">Вітаємо</key>
    <key alias="greeting4">Вітаємо</key>
    <key alias="greeting5">Вітаємо</key>
    <key alias="greeting6">Вітаємо</key>
    <key alias="instruction">Вкажіть ім'я користувача та пароль</key>
    <key alias="timeout">Час сесії закінчився</key>
    <key alias="forgottenPassword">Забули пароль?</key>
    <key alias="forgottenPasswordInstruction">На email-адрес буде надіслано листа з посиланням на скидання пароля</key>
    <key alias="requestPasswordResetConfirmation">Буде надіслано листа з інструкціями щодо скидання пароля на вказаний email-адрес, якщо він збігається з адресою користувача</key>
    <key alias="returnToLogin">Повернутися до форми входу</key>
    <key alias="setPasswordInstruction">Будь ласка, вкажіть новий пароль</key>
    <key alias="setPasswordConfirmation">Ваш пароль оновлено</key>
    <key alias="signInWith">Увійти за допомогою</key>
    <key alias="resetCodeExpired">Посилання, по якому Ви потрапили сюди, неправильне або застаріло</key>
    <key alias="resetPasswordEmailCopySubject">Umbraco: скидання пароля</key>
    <key alias="resetPasswordEmailCopyFormat"><![CDATA[
        <html>
			<head>
				<meta name='viewport' content='width=device-width'>
				<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
			</head>
			<body class='' style='font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; color: #392F54; line-height: 22px; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background: #1d1333; margin: 0; padding: 0;' bgcolor='#1d1333'>
				<style type='text/css'> @media only screen and (max-width: 620px) {table[class=body] h1 {font-size: 28px !important; margin-bottom: 10px !important; } table[class=body] .wrapper {padding: 32px !important; } table[class=body] .article {padding: 32px !important; } table[class=body] .content {padding: 24px !important; } table[class=body] .container {padding: 0 !important; width: 100% !important; } table[class=body] .main {border-left-width: 0 !important; border-radius: 0 !important; border-right-width: 0 !important; } table[class=body] .btn table {width: 100% !important; } table[class=body] .btn a {width: 100% !important; } table[class=body] .img-responsive {height: auto !important; max-width: 100% !important; width: auto !important; } } .btn-primary table td:hover {background-color: #34495e !important; } .btn-primary a:hover {background-color: #34495e !important; border-color: #34495e !important; } .btn  a:visited {color:#FFFFFF;} </style>
				<table border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #1d1333;" bgcolor="#1d1333">
					<tr>
						<td style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding: 24px;" valign="top">
							<table style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
								<tr>
									<td background="https://umbraco.com/umbraco/assets/img/application/logo.png" bgcolor="#1d1333" width="28" height="28" valign="top" style="font-family: sans-serif; font-size: 14px; vertical-align: top;">
										<!--[if gte mso 9]> <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:30px;height:30px;"> <v:fill type="tile" src="https://umbraco.com/umbraco/assets/img/application/logo.png" color="#1d1333" /> <v:textbox inset="0,0,0,0"> <![endif]-->
										<div> </div>
										<!--[if gte mso 9]> </v:textbox> </v:rect> <![endif]-->
									</td>
									<td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top"></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<table border='0' cellpadding='0' cellspacing='0' class='body' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #1d1333;' bgcolor='#1d1333'>
					<tr>
						<td style='font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'> </td>
						<td class='container' style='font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; max-width: 560px; width: 560px; margin: 0 auto; padding: 10px;' valign='top'>
							<div class='content' style='box-sizing: border-box; display: block; max-width: 560px; margin: 0 auto; padding: 10px;'>
								<br>
								<table class='main' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-radius: 3px; background: #FFFFFF;' bgcolor='#FFFFFF'>
									<tr>
										<td class='wrapper' style='font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 50px;' valign='top'>
											<table border='0' cellpadding='0' cellspacing='0' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;'>
												<tr>
													<td style='line-height: 24px; font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'>
														<h1 style='color: #392F54; font-family: sans-serif; font-weight: bold; line-height: 1.4; font-size: 24px; text-align: left; text-transform: capitalize; margin: 0 0 30px;' align='left'>
															Запитано скидання пароля
														</h1>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															Ваше ім'я користувача для входу до адміністративної панелі Umbraco: <strong>%0%</strong>
														</p>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															<table border='0' cellpadding='0' cellspacing='0' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;'>
																<tbody>
																	<tr>
																		<td style='font-family: sans-serif; font-size: 14px; vertical-align: top; border-radius: 5px; text-align: center; background: #35C786;' align='center' bgcolor='#35C786' valign='top'>
																			<a href='%1%' target='_blank' rel='noopener' style='color: #FFFFFF; text-decoration: none; -ms-word-break: break-all; word-break: break-all; border-radius: 5px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 14px; font-weight: bold; text-transform: capitalize; background: #35C786; margin: 0; padding: 12px 30px; border: 1px solid #35c786;'>
																				Натисніть на це посилання, щоб скинути пароль
																			</a>
																		</td>
																	</tr>
																</tbody>
															</table>
														</p>
														<p style='max-width: 400px; display: block; color: #392F54; font-family: sans-serif; font-size: 14px; line-height: 20px; font-weight: normal; margin: 15px 0;'>Якщо Ви не маєте можливості натиснути на посилання, скопіюйте наступну адресу (URL) та вставте в адресний рядок Вашого браузера:</p>
															<table border='0' cellpadding='0' cellspacing='0'>
																<tr>
																	<td style='-ms-word-break: break-all; word-break: break-all; font-family: sans-serif; font-size: 11px; line-height:14px;'>
																		<font style="-ms-word-break: break-all; word-break: break-all; font-size: 11px; line-height:14px;">
																			<a style='-ms-word-break: break-all; word-break: break-all; color: #392F54; text-decoration: underline; font-size: 11px; line-height:15px;' href='%1%'>%1%</a>
																		</font>
																	</td>
																</tr>
															</table>
														</p>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<br><br><br>
							</div>
						</td>
						<td style='font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'> </td>
					</tr>
				</table>
			</body>
		</html>
      ]]></key>
  </area>
  <area alias="main">
    <key alias="dashboard">Панель керування</key>
    <key alias="sections">Розділи</key>
    <key alias="tree">Вміст</key>
  </area>
  <area alias="media">
    <key alias="clickToUpload">Натисніть, щоб завантажити</key>
    <key alias="disallowedFileType">Неможливе завантаження цього файлу, цей тип файлів не дозволяється для завантаження</key>
    <key alias="orClickHereToUpload">або натисніть тут, щоб вибрати файли</key>
    <key alias="maxFileSize">Максимально допустимий розмір файлу: </key>
    <key alias="mediaRoot">Початковий вузол медіа</key>
  </area>
  <area alias="mediaPicker">
    <key alias="pickedTrashedItem">Вибрано медіа-елемент, який в даний час видалено або знаходиться в кошику</key>
    <key alias="pickedTrashedItems">Вибрані медіа-елементи, які в даний час видалені або знаходяться в кошику</key>
  </area>
  <area alias="member">
    <key alias="createNewMember">Створити нового учасника</key>
    <key alias="allMembers">Всі учасники</key>
  </area>
  <area alias="modelsBuilder">
    <key alias="buildingModels">Побудова моделей</key>
    <key alias="waitingMessage">це може зайняти деякий час, будь ласка, зачекайте</key>
    <key alias="modelsGenerated">Моделі побудовані</key>
    <key alias="modelsGeneratedError">Моделі не можуть бути побудовані</key>
    <key alias="modelsExceptionInUlog">Процес побудови моделей завершився помилкою, подробиці у системному журналі Umbraco</key>
  </area>
  <area alias="moveOrCopy">
    <key alias="choose">Виберіть сторінку...</key>
    <key alias="copyDone">Вузол %0% був скопійований в %1%</key>
    <key alias="copyTo">Виберіть, куди має бути скопійований вузол %0%</key>
    <key alias="moveDone">Вузол %0% був переміщений в %1%</key>
    <key alias="moveTo">Виберіть, куди має бути переміщений вузол %0%</key>
    <key alias="nodeSelected">був обраний як батьківський вузол для нового елемента, натисніть 'Ок'.</key>
    <key alias="noNodeSelected">Не вибраний вузол! Будь ласка, оберіть вузол призначення, перш ніж натиснути 'Ок'.</key>
    <key alias="notAllowedAtRoot">Поточний вузол не може бути розміщений безпосередньо в корені дерева</key>
    <key alias="notAllowedByContentType">Поточний вузол не може бути розміщений у вибраному Вами вузлі через невідповідність типів.</key>
    <key alias="notAllowedByPath">Поточний вузол не може бути переміщений усередину своїх дочірніх вузлів</key>
    <key alias="notValid">Ця дія не може бути здійснена, оскільки Ви не маєте достатніх прав для здійснення дій над одним або більше дочірніми документами.</key>
    <key alias="relateToOriginal">Пов'язати нові копії з оригіналами</key>
  </area>
  <area alias="notifications">
    <key alias="editNotifications">Ви можете змінити повідомлення для %0%</key>
    <key alias="mailBody"><![CDATA[
		Вітаємо, %0%

		Це автоматично згенероване повідомлення.
		Операція '%1%'
		була зроблена на сторінці '%2%' користувачем '%3%'.

		Ви можете побачити зміни та відредагувати, перейшовши за посиланням http://%4%/#/content/content/edit/%5%.

		Успіхів!

		Генератор повідомлень Umbraco.
		]]></key>
    <key alias="mailBodyHtml"><![CDATA[
        <html>
			<head>
				<meta name='viewport' content='width=device-width'>
				<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
			</head>
			<body class='' style='font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; color: #392F54; line-height: 22px; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background: #1d1333; margin: 0; padding: 0;' bgcolor='#1d1333'>
				<style type='text/css'> @media only screen and (max-width: 620px) {table[class=body] h1 {font-size: 28px !important; margin-bottom: 10px !important; } table[class=body] .wrapper {padding: 32px !important; } table[class=body] .article {padding: 32px !important; } table[class=body] .content {padding: 24px !important; } table[class=body] .container {padding: 0 !important; width: 100% !important; } table[class=body] .main {border-left-width: 0 !important; border-radius: 0 !important; border-right-width: 0 !important; } table[class=body] .btn table {width: 100% !important; } table[class=body] .btn a {width: 100% !important; } table[class=body] .img-responsive {height: auto !important; max-width: 100% !important; width: auto !important; } } .btn-primary table td:hover {background-color: #34495e !important; } .btn-primary a:hover {background-color: #34495e !important; border-color: #34495e !important; } .btn  a:visited {color:#FFFFFF;} </style>
				<table border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #1d1333;" bgcolor="#1d1333">
					<tr>
						<td style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding: 24px;" valign="top">
							<table style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
								<tr>
									<td background="https://umbraco.com/umbraco/assets/img/application/logo.png" bgcolor="#1d1333" width="28" height="28" valign="top" style="font-family: sans-serif; font-size: 14px; vertical-align: top;">
										<!--[if gte mso 9]> <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:30px;height:30px;"> <v:fill type="tile" src="https://umbraco.com/umbraco/assets/img/application/logo.png" color="#1d1333" /> <v:textbox inset="0,0,0,0"> <![endif]-->
										<div> </div>
										<!--[if gte mso 9]> </v:textbox> </v:rect> <![endif]-->
									</td>
									<td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top"></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<table border='0' cellpadding='0' cellspacing='0' class='body' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #1d1333;' bgcolor='#1d1333'>
					<tr>
						<td style='font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'> </td>
						<td class='container' style='font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; max-width: 560px; width: 560px; margin: 0 auto; padding: 10px;' valign='top'>
							<div class='content' style='box-sizing: border-box; display: block; max-width: 560px; margin: 0 auto; padding: 10px;'>
								<br>
								<table class='main' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-radius: 3px; background: #FFFFFF;' bgcolor='#FFFFFF'>
									<tr>
										<td class='wrapper' style='font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 50px;' valign='top'>
											<table border='0' cellpadding='0' cellspacing='0' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;'>
												<tr>
													<td style='line-height: 24px; font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'>
														<h1 style='color: #392F54; font-family: sans-serif; font-weight: bold; line-height: 1.4; font-size: 24px; text-align: left; text-transform: capitalize; margin: 0 0 30px;' align='left'>
															Вітаємо, %0%,
														</h1>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															Це автоматично згенероване повідомлення, відправлене, щоб повідомити Вас про те, що операція <strong>'%1%'</strong> була виконана на сторінці <a style="color: #392F54; text-decoration: none; -ms-word-break: break-all; word-break: break-all;" href="http://%4%/#/content/content/edit/%5%"><strong>'%2%'</strong></a> користувачем <strong>'%3%'</strong>
														</p>
														<table border='0' cellpadding='0' cellspacing='0' class='btn btn-primary' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;'>
															<tbody>
																<tr>
																	<td align='left' style='font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px;' valign='top'>
																		<table border='0' cellpadding='0' cellspacing='0' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;'><tbody><tr>
																			<td style='font-family: sans-serif; font-size: 14px; vertical-align: top; border-radius: 5px; text-align: center; background: #35C786;' align='center' bgcolor='#35C786' valign='top'>
																				<a href='http://%4%/#/content/content/edit/%5%' target='_blank' rel='noopener' style='color: #FFFFFF; text-decoration: none; -ms-word-break: break-all; word-break: break-all; border-radius: 5px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 14px; font-weight: bold; text-transform: capitalize; background: #35C786; margin: 0; padding: 12px 30px; border: 1px solid #35c786;'>ВНЕСТИ ЗМІНИ</a> </td> </tr></tbody></table>
																	</td>
																</tr>
															</tbody>
														</table>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															<h3>Огляд оновлення:</h3>
															<table style="width: 100%;">
																 %6%
															</table>
														</p>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															Вдалого дня!<br /><br />
															До Ваших послуг, поштовий робот Umbraco
														</p>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<br><br><br>
							</div>
						</td>
						<td style='font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'> </td>
					</tr>
				</table>
			</body>
		</html>
      ]]></key>
    <key alias="mailSubject">[%0%] Сповіщення про операцію %1% над документом %2%</key>
    <key alias="notifications">Сповіщення</key>
  </area>
  <area alias="packager">
    <key alias="chooseLocalPackageText"><![CDATA[
		Виберіть файл пакета на своєму комп'ютері, натиснувши кнопку 'Огляд'<br />
		та вказавши на потрібний файл. Пакети Umbraco зазвичай є архівами із розширенням '.zip'.
		]]></key>
    <key alias="packageLicense">Ліцензія</key>
    <key alias="installedPackages">Встановлені пакети</key>
    <key alias="noPackages">Жодного пакету ще не встановлено</key>
    <key alias="noPackagesDescription"><![CDATA[Ви поки що не встановлювали жодного пакету. Ви можете встановити локальний пакет, вибравши файл на Вашому комп'ютері, так і пакет з репозиторію, натиснувши на значок <strong>'Packages'</strong> зверху праворуч]]></key>
    <key alias="packageSearch">Пошук по пакетам</key>
    <key alias="packageSearchResults">Результати пошуку по</key>
    <key alias="packageNoResults">Нічого не знайдено за запитом</key>
    <key alias="packageNoResultsDescription">Будь ласка, повторіть пошук, уточнивши запит, або скористайтесь переглядом за категоріями</key>
    <key alias="packagesPopular">Популярні</key>
    <key alias="packagesNew">Нещодавно створені</key>
    <key alias="packageHas">має на рахунку</key>
    <key alias="packageKarmaPoints">очок карми</key>
    <key alias="packageInfo">Інформація</key>
    <key alias="packageOwner">Власник</key>
    <key alias="packageContrib">Співавтори</key>
    <key alias="packageCreated">Створено</key>
    <key alias="packageCurrentVersion">Поточна версія</key>
    <key alias="packageNetVersion">Версія .NET</key>
    <key alias="packageDownloads">Завантажень</key>
    <key alias="packageLikes">Подобається</key>
    <key alias="packageCompatibility">Сумісність</key>
    <key alias="packageCompatibilityDescription">Цей пакет сумісний з наступними версіями Umbraco за повідомленнями учасників спільноти. Повна сумісність не гарантується для версій зі значенням нижче 100%</key>
    <key alias="packageExternalSources">Зовнішні джерела</key>
    <key alias="packageAuthor">Автор</key>
    <key alias="packageDocumentation">Документація (опис)</key>
    <key alias="packageMetaData">Мета-дані пакету</key>
    <key alias="packageName">Назва пакету</key>
    <key alias="packageNoItemsHeader">Пакет нічого не містить</key>
    <key alias="packageNoItemsText"><![CDATA[Цей файл пакета не містить жодного елемента
		для видалення.<br/><br/>Ви можете безпечно видалити цей пакет із системи, натиснувши на кнопку "Деінсталювати пакет".]]></key>
    <key alias="packageOptions">Опції пакету</key>
    <key alias="packageReadme">Короткий огляд пакету</key>
    <key alias="packageRepository">Репозиторій пакету</key>
    <key alias="packageUninstallConfirm">Підтвердження деінсталяції пакету</key>
    <key alias="packageUninstalledHeader">Пакет деінстальований</key>
    <key alias="packageUninstalledText">Вказаний пакет успішно видалено із системи</key>
    <key alias="packageUninstallHeader">Деінсталювати пакет</key>
    <key alias="packageUninstallText"><![CDATA[Сейчас Ви можете зняти позначки з тих опцій пакета, які НЕ бажаєте видаляти. Після натискання кнопки "Підтвердження деінсталяції" всі зазначені опції будуть видалені.<br />
		<span style="color: Red; font-weight: bold;">Зверніть увагу:</span> всі документи, медіа-файли та інший контент, що залежить від цього пакета, перестане нормально працювати, що може призвести до нестабільної поведінки системи,
		тому видаляйте пакети дуже обережно. За наявності сумнівів, зв'яжіться з автором пакета.]]></key>
    <key alias="packageVersion">Версія пакету</key>
  </area>
  <area alias="paste">
    <key alias="doNothing">Вставити, повністю зберігши форматування (не рекомендується)</key>
    <key alias="errorMessage">Текст, який Ви намагаєтесь вставити, містить спеціальні символи та/або елементи форматування. Це можливо, якщо ви вставляєте текст, скопійований з Microsoft Word або подібного редактора. Система може видалити ці елементи автоматично, щоб зробити текст, що вставляється, більш придатним для веб-публікації.</key>
    <key alias="removeAll">Вставити як простий текст без форматування</key>
    <key alias="removeSpecialFormattering">Вставити з очищенням форматування (рекомендується)</key>
  </area>
  <area alias="placeholders">
    <key alias="confirmPassword">Підтвердіть пароль</key>
    <key alias="email">Вкажіть Ваш email...</key>
    <key alias="enterDescription">Вкажіть опис...</key>
    <key alias="enteremail">Вкажіть email...</key>
    <key alias="enterMessage">Вкажіть повідомлення...</key>
    <key alias="entername">Вкажіть ім'я...</key>
    <key alias="enterTags">Вкажіть теги (натискайте Enter після кожного тега)...</key>
    <key alias="enterusername">Вкажіть ім'я користувача...</key>
    <key alias="filter">Вкажіть фільтр...</key>
    <key alias="label">Мітка...</key>
    <key alias="nameentity">Назвіть %0%...</key>
    <key alias="password">Вкажіть пароль</key>
    <key alias="search">Що шукати...</key>
    <key alias="username">Вкажіть ім'я користувача</key>
    <key alias="usernameHint">Ім'я користувача (зазвичай це Ваша email-адреса)</key>
  </area>
  <area alias="prompt">
    <key alias="stay">Залишитися</key>
    <key alias="discardChanges">Відмінити зміни</key>
    <key alias="unsavedChanges">Є незбережені зміни</key>
    <key alias="unsavedChangesWarning">Ви впевнені, що хочете піти з цієї сторінки? - на ній є незбережені зміни</key>
  </area>
  <area alias="publicAccess">
    <key alias="paAdvanced">Розширений: Захист на основі ролей (груп)</key>
    <key alias="paAdvancedHelp"><![CDATA[Застосовуйте, якщо бажаєте контролювати доступ до документа на основі рольової моделі безпеки,<br /> із використанням груп учасників Umbraco.]]></key>
    <key alias="paAdvancedNoGroups">Вам необхідно створити хоча б одну групу для застосування рольової моделі безпеки.</key>
    <key alias="paErrorPage">Сторінка повідомлення про помилку</key>
    <key alias="paErrorPageHelp">Використовується у випадку, коли користувач авторизований у системі, але не має доступу до документа.</key>
    <key alias="paHowWould">Виберіть спосіб обмеження доступу до документа</key>
    <key alias="paIsProtected">Правила доступу до документа %0% встановлені</key>
    <key alias="paIsRemoved">Правила доступу до документа %0% видалені</key>
    <key alias="paLoginPage">Сторінка авторизації (входу)</key>
    <key alias="paLoginPageHelp">Використовуйте як сторінку з формою для авторизації користувачів</key>
    <key alias="paRemoveProtection">Зняти захист</key>
    <key alias="paSelectPages">Виберіть сторінки авторизації та повідомлень про помилки</key>
    <key alias="paSelectRoles">Виберіть ролі користувачів, які мають доступ до документа</key>
    <key alias="paSetLogin">Встановіть ім'я користувача та пароль для доступу до цього документа</key>
    <key alias="paSimple">Простий: Захист по імені користувача та паролю</key>
    <key alias="paSimpleHelp">Застосовуйте, якщо хочете встановити найпростіший спосіб доступу до документа - явно вказані ім'я користувача та пароль</key>
  </area>
  <area alias="publish">
    <key alias="contentPublishedFailedAwaitingRelease"><![CDATA[
      Документ %0% не можна опубліковати зараз, оскільки для нього встановлено розклад публікації.
    ]]></key>
    <key alias="contentPublishedFailedByEvent"><![CDATA[
		Документ %0% не можна опубліковати. Операцію скасував встановлений у системі пакет доповнень.
		]]></key>
    <key alias="contentPublishedFailedExpired"><![CDATA[
      Документ %0% не можна опубліковати, тому що поточна інформація в ньому застаріла.
    ]]></key>
    <key alias="contentPublishedFailedByParent"><![CDATA[
      Документ %0% не можна опубліковати, тому що не опубліковано його батьківський документ.
    ]]></key>
    <key alias="contentPublishedFailedInvalid"><![CDATA[
      Документ %0% не можна опубліковати, тому що не всі його властивості пройшли перевірку згідно з встановленими правилами валідації.
    ]]></key>
    <key alias="includeUnpublished">Включно з неопублікованими дочірніми документами</key>
    <key alias="inProgress">Йде публікація. Будь ласка зачекайте...</key>
    <key alias="inProgressCounter">%0% з %1% документів опубліковано...</key>
    <key alias="nodePublish">Документ %0% опубліковано.</key>
    <key alias="nodePublishAll">Документ %0% та його дочірні документи опубліковані</key>
    <key alias="publishAll">Опублікувати документ %0% та всі його дочірні документи</key>
    <key alias="publishHelp"><![CDATA[Натисніть кнопку <em>Опублікувати</em> для публікації документа <strong>%0%</strong>.
		Таким чином Ви зробите вміст документа доступним для перегляду.<br /><br />
		Ви можете опублікувати цей документ та всі його дочірні документи, відмітивши опцію <em>Опублікувати усі дочірні документи</em>.
		Щоб опублікувати раніше неопубліковані документи серед дочірніх, відмітьте опцію <em>Включаючи неопубліковані дочірні документи</em>.
		]]></key>
  </area>
  <area alias="redirectUrls">
    <key alias="disableUrlTracker">Зупинити відстеження URL</key>
    <key alias="enableUrlTracker">Запустити відстеження URL</key>
    <key alias="originalUrl">Початковий URL</key>
    <key alias="redirectedTo">Перенаправлено в</key>
    <key alias="noRedirects">На даний момент немає жодного перенаправлення</key>
    <key alias="noRedirectsDescription">Якщо опублікований документ перейменовується або змінює розташування в дереві, а отже, змінюється адреса (URL), автоматично створюється перенаправлення на нове розташування цього документа.</key>
    <key alias="redirectRemoved">Перенаправлення видалено.</key>
    <key alias="redirectRemoveError">Помилка видалення перенаправлення.</key>
    <key alias="confirmDisable">Ви впевнені, що хочете зупинити відстеження URL?</key>
    <key alias="disabledConfirm">Відстеження URL зараз зупинено.</key>
    <key alias="disableError">Помилка зупинки відстеження URL, докладніші відомості знаходяться в системному журналі.</key>
    <key alias="enabledConfirm">Відстеження URL зараз запущено.</key>
    <key alias="enableError">Помилка запуску відстеження URL, докладніші відомості знаходяться в системному журналі.</key>
  </area>
  <area alias="relatedlinks">
    <key alias="caption">Заголовок</key>
    <key alias="captionPlaceholder">Вкажіть заголовок посилання</key>
    <key alias="chooseInternal">вибрати сторінку сайту</key>
    <key alias="enterExternal">вказати зовнішнє посилання</key>
    <key alias="externalLinkPlaceholder">Вкажіть посилання</key>
    <key alias="link">Посилання</key>
    <key alias="newWindow">Відкрити у новому вікні</key>
  </area>
  <area alias="renamecontainer">
    <key alias="renamed">Перейменована</key>
    <key alias="enterNewFolderName">Вкажіть тут нову назву для папки</key>
    <key alias="folderWasRenamed">'%0%' була перейменована на '%1%'</key>
  </area>
  <area alias="rollback">
    <key alias="diffHelp"><![CDATA[Тут показано різницю між останньою версією документа і обраною Вами версією.<br /><del>Червоним</del> відзначено текст, якого вже немає в останній версії, <ins>зеленим</ins> - текст, який був доданий]]></key>
    <key alias="documentRolledBack">Зроблено відкат до ранньої версії</key>
    <key alias="htmlHelp">Поточна версія показана як HTML. Щоб переглянути відмінності у версіях, виберіть режим порівняння</key>
    <key alias="rollbackTo">Відкатати до версії</key>
    <key alias="selectVersion">Виберіть версію</key>
    <key alias="view">Перегляд</key>
  </area>
  <area alias="scripts">
    <key alias="editscript">Редагувати файл скрипта</key>
  </area>
  <area alias="sections">
    <key alias="concierge">Консьєрж</key>
    <key alias="content">Вміст</key>
    <key alias="courier">Кур'єр</key>
    <key alias="developer">Розробка</key>
    <key alias="forms">Форми</key>
    <key alias="help" version="7.0">Допомога</key>
    <key alias="installer">Майстер конфігурування Umbraco</key>
    <key alias="media">Медіа-матеріали</key>
    <key alias="member">Учасники</key>
    <key alias="newsletters">Розсилки</key>
    <key alias="settings">Налаштування</key>
    <key alias="statistics">Статистика</key>
    <key alias="translation">Переклад</key>
    <key alias="users">Користувачі</key>
  </area>
  <area alias="settings">
    <key alias="addIcon">Додати піктограму</key>
    <key alias="contentTypeEnabled">Батьківський тип контенту дозволено</key>
    <key alias="contentTypeUses">Цей тип контенту використовує</key>
    <key alias="defaulttemplate">Шаблон за замовчуванням</key>
    <key alias="importDocumentTypeHelp">Щоб імпортувати тип документа, знайдіть файл ".udt" на своєму комп'ютері, натиснувши кнопку "Огляд", потім натисніть "Імпортувати" (на наступному екрані буде запитано підтвердження для цієї операції).</key>
    <key alias="newtabname">Заголовок нової вкладки</key>
    <key alias="nodetype">Тип вузла (документу)</key>
    <key alias="noPropertiesDefinedOnTab">Для цієї вкладки не визначено характеристики. Клацніть на посилання "Click here to add a new property" зверху, щоб створити нову властивість.</key>
    <key alias="objecttype">Тип</key>
    <key alias="script">Скрипт</key>
    <key alias="stylesheet">Стилі CSS</key>
    <key alias="tab">Вкладка</key>
    <key alias="tabname">Заголовок вкладки</key>
    <key alias="tabs">Вкладки</key>
  </area>
  <area alias="shortcuts">
    <key alias="addGroup">Додати вкладку</key>
    <key alias="addProperty">Додати властивість</key>
    <key alias="addEditor">Додати редактор</key>
    <key alias="addTemplate">Додати шаблон</key>
    <key alias="addChildNode">Додати дочірній вузол</key>
    <key alias="addChild">Додати дочірній</key>
    <key alias="editDataType">Змінити тип даних</key>
    <key alias="navigateSections">Навігація по розділам</key>
    <key alias="shortcut">Ярлики</key>
    <key alias="showShortcuts">показати ярлики</key>
    <key alias="toggleListView">У форматі списку</key>
    <key alias="toggleAllowAsRoot">Дозволити як кореневий</key>
    <key alias="commentLine">Закоментувати/розкоментувати рядки</key>
    <key alias="removeLine">Видалити рядок</key>
    <key alias="copyLineUp">Копіювати рядки вгору</key>
    <key alias="copyLineDown">Копіювати рядки вниз</key>
    <key alias="moveLineUp">Перемістити рядки вгору</key>
    <key alias="moveLineDown">Перемістити рядки вниз</key>
    <key alias="generalHeader">Загальне</key>
    <key alias="editorHeader">Редактор</key>
  </area>
  <area alias="sort">
    <key alias="sortOrder">Порядок сортування</key>
    <key alias="sortCreationDate">Дата створення</key>
    <key alias="sortDone">Сортування завершено</key>
    <key alias="sortHelp">Перетягуйте елементи на потрібне місце вгору або вниз для визначення потрібного порядку сортування. Також можна використовувати заголовки стовпців, щоб відсортувати всі елементи одразу.</key>
    <key alias="sortPleaseWait"><![CDATA[Зачекайте, будь ласка... Сторінки сортуються, це може зайняти деякий час.]]></key>
  </area>
  <area alias="speechBubbles">
    <key alias="contentPublishedFailedByEvent">Процес публікації скасовано встановленим пакетом доповнень.</key>
    <key alias="contentTypeDublicatePropertyType">Така властивість вже існує.</key>
    <key alias="contentTypePropertyTypeCreated">Властивість створена</key>
    <key alias="contentTypePropertyTypeCreatedText"><![CDATA[Назва: %0% <br /> Тип даних: %1%]]></key>
    <key alias="contentTypePropertyTypeDeleted">Властивість видалена</key>
    <key alias="contentTypeSavedHeader">Тип документа збережено</key>
    <key alias="contentTypeTabCreated">Вкладка створена</key>
    <key alias="contentTypeTabDeleted">Вкладка видалена</key>
    <key alias="contentTypeTabDeletedText">Вкладка з ідентифікатором (id): %0% видалена</key>
    <key alias="contentUnpublished">Документ прихований (публікацію скасовано)</key>
    <key alias="cssErrorHeader">Стиль CSS не збережено</key>
    <key alias="cssSavedHeader">Стиль CSS збережено</key>
    <key alias="cssSavedText">Стиль CSS збережено без помилок</key>
    <key alias="dataTypeSaved">Тип даних збережено</key>
    <key alias="dictionaryItemSaved">Стаття у словнику збережена</key>
    <key alias="editContentPublishedFailedByParent">Публікацію не завершено, оскільки батьківський документ не опубліковано</key>
    <key alias="editContentPublishedHeader">Документ опубліковано</key>
    <key alias="editContentPublishedText">і є доступним</key>
    <key alias="editContentSavedHeader">Документ збережено</key>
    <key alias="editContentSavedText">Не забудьте опублікувати, щоб зробити доступним</key>
    <key alias="editContentSendToPublish">Надіслано на затвердження</key>
    <key alias="editContentSendToPublishText">Зміни надіслані на затвердження</key>
    <key alias="editMediaSaved">Медіа-елемент збережено</key>
    <key alias="editMediaSavedText">Медіа-елемент збережено без помилок</key>
    <key alias="editMemberSaved">Учасник збережений</key>
    <key alias="editStylesheetPropertySaved">Правило стилю CSS збережено</key>
    <key alias="editStylesheetSaved">Стиль CSS збережено</key>
    <key alias="editTemplateSaved">Шаблон збережено</key>
    <key alias="editUserError">При збереженні користувача виникла помилка (перевірте журнали помилок)</key>
    <key alias="editUserGroupSaved">Група користувачів збережена</key>
    <key alias="editUserSaved">Користувач збережений</key>
    <key alias="editUserTypeSaved">Тип користувачів збережено</key>
    <key alias="fileErrorHeader">Файл не збережено</key>
    <key alias="fileErrorText">Файл не може бути збережений. Будь ласка, перевірте налаштування файлових дозволів</key>
    <key alias="fileSavedHeader">Файл збережено</key>
    <key alias="fileSavedText">Файл збережено без помилок</key>
    <key alias="invalidUserPermissionsText">У поточного користувача недостатньо прав, неможливо завершити операцію</key>
    <key alias="languageSaved">Мова збережена</key>
    <key alias="mediaTypeSavedHeader">Тип медіа збережено</key>
    <key alias="memberTypeSavedHeader">Тип учасника збережено</key>
    <key alias="operationCancelledHeader">Скасовано</key>
    <key alias="operationCancelledText">Операцію скасовано встановленим стороннім розширенням або блоком коду</key>
    <key alias="operationFailedHeader">Помилка</key>
    <key alias="operationSavedHeader">Збережено</key>
    <key alias="partialViewErrorHeader">Представлення не збережено</key>
    <key alias="partialViewErrorText">Виникла помилка при збереженні файлу</key>
    <key alias="partialViewSavedHeader">Представлення збережено</key>
    <key alias="partialViewSavedText">Представлення збережено без помилок</key>
    <key alias="permissionsSavedFor">Права доступу збережені для</key>
    <key alias="templateErrorHeader">Шаблон не збережено</key>
    <key alias="templateErrorText">Будь ласка, перевірте, що немає двох шаблонів з одним і тим самим аліасом (назвою)</key>
    <key alias="templateSavedHeader">Шаблон збережено</key>
    <key alias="templateSavedText">Шаблон збережено без помилок</key>
    <key alias="validationFailedHeader">Перевірка значень</key>
    <key alias="validationFailedMessage">Помилки, знайдені під час перевірки значень, мають бути виправлені, щоб було можливо зберегти документ</key>
    <key alias="deleteUserGroupsSuccess">Видалено %0% груп користувачів</key>
    <key alias="deleteUserGroupSuccess">'%0%' була видалена</key>
    <key alias="enableUsersSuccess">Активовано %0% користувачів</key>
    <key alias="disableUsersSuccess">Заблоковано %0% користувачів</key>
    <key alias="enableUserSuccess">'%0%' активований</key>
    <key alias="disableUserSuccess">'%0%' заблокований</key>
    <key alias="setUserGroupOnUsersSuccess">Групи користувачів встановлені</key>
    <key alias="unlockUsersSuccess">Розблоковано %0% користувачів</key>
    <key alias="unlockUserSuccess">'%0%' зараз розблоковано</key>
    <key alias="memberExportedSuccess">Дані учасника успішно експортовано у файл</key>
    <key alias="memberExportedError">Під час експорту даних учасника сталася помилка</key>
  </area>
  <area alias="stylesheet">
    <key alias="aliasHelp">Використовується синтаксис селекторів CSS, наприклад: h1, .redHeader, .blueTex</key>
    <key alias="editstylesheet">Змінити стиль CSS</key>
    <key alias="editstylesheetproperty">Змінити правило стилю CSS</key>
    <key alias="nameHelp">Назва правила для відображення у редакторі документа</key>
    <key alias="preview">Попередній перегляд</key>
    <key alias="styles">Стилі</key>
  </area>
  <area alias="template">
    <key alias="edittemplate">Змінити шаблон</key>
    <key alias="insertSections">Секції</key>
    <key alias="insertContentArea">Вставити контент-область</key>
    <key alias="insertContentAreaPlaceHolder">Вставити контейнер (placeholder)</key>
    <key alias="insert">Вставити</key>
    <key alias="insertDesc">Виберіть, що хочете вставити в шаблон</key>
    <key alias="insertDictionaryItem">Статтю словника</key>
    <key alias="insertDictionaryItemDesc">Стаття словника - це контейнер для частини тексту, що перекладається різними мовами, це дозволяє спростити створення багатомовних сайтів.</key>
    <key alias="insertMacro">Макрос</key>
    <key alias="insertMacroDesc">
      Макроси - це компоненти, що мають налаштування, які добре підходять для
      реалізації блоків, що перевикористовуються, (особливо, якщо необхідно змінювати їх зовнішній вигляд і/або поведінку за допомогою параметрів)
      таких як галереї, форми, списки тощо.
    </key>
    <key alias="insertPageField">Значення поля</key>
    <key alias="insertPageFieldDesc">Відображає значення вказаного поля даних поточної сторінки,
       з можливістю вказати альтернативні поля та/або підстановку константи.
    </key>
    <key alias="insertPartialView">Часткове представлення</key>
    <key alias="insertPartialViewDesc">
      Часткове представлення - це шаблон в окремому файлі, який може бути викликаний для відображення всередині
      іншого шаблону, добре підходить для реалізації фрагментів розмітки, що перевикористовуються, або для розбиття складних шаблонів на складові частини.
    </key>
    <key alias="mastertemplate">Майстер-шаблон</key>
    <key alias="noMaster">Не вибраний</key>
    <key alias="renderBody">Вставити дочірній шаблон</key>
    <key alias="renderBodyDesc"><![CDATA[
     Відображає вміст дочірнього шаблону за допомогою вставки конструкції
     <code>@RenderBody()</code> у вибраному місці.
      ]]></key>
    <key alias="defineSection">Визначити іменовану секцію</key>
    <key alias="defineSectionDesc"><![CDATA[
         Визначає спеціальну область шаблону як іменовану секцію шляхом огортання її в конструкцію
          <code>@section { ... }</code>. Така секція може бути відображена в потрібному місці батьківського шаблону
          за допомогою конструкції <code>@RenderSection</code>.
      ]]></key>
    <key alias="renderSection">Вставити іменовану секцію</key>
    <key alias="renderSectionDesc"><![CDATA[
      Відображає вміст іменованої області дочірнього шаблону за допомогою вставки конструкції <code>@RenderSection(name)</code>.
      Таким чином, з дочірнього шаблону відображається вміст усередині конструкції. <code>@section [name]{ ... }</code>.
      ]]></key>
    <key alias="sectionName">Назва секції</key>
    <key alias="sectionMandatory">Секція є обов'язковою</key>
    <key alias="sectionMandatoryDesc"><![CDATA[
      Якщо секція позначена як обов'язкова, дочірній шаблон повинен обов'язково містити її визначення <code>@section</code>, інакше генерується помилка.
    ]]></key>
    <key alias="queryBuilder">Генератор запитів</key>
    <key alias="itemsReturned">елементів в результаті, за</key>
    <key alias="iWant">Мені потрібні</key>
    <key alias="allContent">всі документи</key>
    <key alias="contentOfType">документи типу "%0%"</key>
    <key alias="from">з</key>
    <key alias="websiteRoot">всього сайту</key>
    <key alias="where">де</key>
    <key alias="and">та</key>
    <key alias="is">є</key>
    <key alias="isNot">не є</key>
    <key alias="before">до</key>
    <key alias="beforeIncDate">до (включаючи вибрану дату)</key>
    <key alias="after">після</key>
    <key alias="afterIncDate">після (включаючи обрану дату)</key>
    <key alias="equals">дорівнює</key>
    <key alias="doesNotEqual">не дорівнює</key>
    <key alias="contains">містить</key>
    <key alias="doesNotContain">не містить</key>
    <key alias="greaterThan">більше ніж</key>
    <key alias="greaterThanEqual">більше або дорівнює</key>
    <key alias="lessThan">менше ніж</key>
    <key alias="lessThanEqual">менше або дорівнює</key>
    <key alias="id">Id</key>
    <key alias="name">Назва</key>
    <key alias="createdDate">Створено</key>
    <key alias="lastUpdatedDate">Оновлено</key>
    <key alias="orderBy">сортувати</key>
    <key alias="ascending">за зростанням</key>
    <key alias="descending">за спаданням</key>
    <key alias="template">Шаблон</key>
  </area>
  <area alias="templateEditor">
    <key alias="addDefaultValue">Додати значення за замовчуванням</key>
    <key alias="defaultValue">Значення за замовчуванням</key>
    <key alias="alternativeField">Поле заміни</key>
    <key alias="alternativeText">Значення за замовчуванням</key>
    <key alias="casing">Реєстр</key>
    <key alias="chooseField">Вибрати поле</key>
    <key alias="convertLineBreaks">Перетворити розриви рядків</key>
    <key alias="convertLineBreaksHelp">Змінює розриви рядків на тег html 'br'</key>
    <key alias="customFields">Користувальницькі</key>
    <key alias="dateOnly">Тільки дата</key>
    <key alias="encoding">Кодування</key>
    <key alias="formatAsDate">Форматувати як дату</key>
    <key alias="htmlEncode">Кодування HTML</key>
    <key alias="htmlEncodeHelp">Замінює спецсимволи еквівалентами у форматі HTML</key>
    <key alias="insertedAfter">Буде вставлено після поля</key>
    <key alias="insertedBefore">Буде вставлено перед полем</key>
    <key alias="lowercase">У нижньому регістрі</key>
    <key alias="none">-Не вказано-</key>
    <key alias="outputSample">Приклад результату</key>
    <key alias="postContent">Вставити після поля</key>
    <key alias="preContent">Вставити перед полем</key>
    <key alias="recursive">Рекурсивно</key>
    <key alias="recursiveDescr">Так, використовувати рекурсію</key>
    <key alias="standardFields">Стандартні</key>
    <key alias="uppercase">У верхньому регістрі</key>
    <key alias="urlEncode">Кодування URL</key>
    <key alias="urlEncodeHelp">Форматування спеціальних символів у URL</key>
    <key alias="usedIfAllEmpty">Це значення буде використано лише якщо попередні поля порожні</key>
    <key alias="usedIfEmpty">Це значення буде використано лише якщо первинне поле порожнє</key>
    <key alias="withTime">Дата і час</key>
  </area>
  <area alias="textbox">
    <key alias="characters_left">символів залишилося</key>
  </area>
  <area alias="translation">
    <key alias="details">Подробиці перекладу</key>
    <key alias="DownloadXmlDTD">Загрузити xml DTD</key>
    <key alias="fields">Поля</key>
    <key alias="includeSubpages">Включити дочірні документи</key>
    <key alias="mailBody"><![CDATA[
		Вітаємо, %0%.

		Цей автоматично згенерований лист був відправлений, щоб поінформувати Вас про те,
		що документ '%1%' був надісланий для перекладу на '%5%' мову користувачем %2%.

		Перейдіть за посиланням http://%3%/translation/details.aspx?id=%4% для редагування.

		Або авторизуйтесь для перегляду Ваших завдань з перекладу
		http://%3%.

		Успіхів!

		Генератор повідомлень Umbraco.
		]]></key>
    <key alias="noTranslators">Користувачів-перекладачів не виявлено. Будь ласка, створіть користувача з роллю перекладача, перш ніж надсилати вміст на переклад</key>
    <key alias="pageHasBeenSendToTranslation">Документ '%0%' був відправлений на переклад</key>
    <key alias="sendToTranslate">Надіслати документ '%0%' на переклад</key>
    <key alias="totalWords">Усього слів</key>
    <key alias="translateTo">Перекласти на</key>
    <key alias="translationDone">Переклад завершено.</key>
    <key alias="translationDoneHelp">Ви можете переглянути документи, перекладені Вами, натиснувши посилання нижче. Якщо буде знайдено оригінал документа, Ви побачите його та переведений варіант у режимі порівняння.</key>
    <key alias="translationFailed">Переклад не збережено, файл xml може бути пошкоджено</key>
    <key alias="translationOptions">Опції перекладу</key>
    <key alias="translator">Перекладач</key>
    <key alias="uploadTranslationXml">Завантажити перекладений xml</key>
  </area>
  <area alias="treeHeaders">
    <key alias="cacheBrowser">Огляд кешу</key>
    <key alias="content">Матеріали</key>
    <key alias="contentBlueprints">Шаблони вмісту</key>
    <key alias="contentRecycleBin">Корзина</key>
    <key alias="createdPackages">Створені пакети</key>
    <key alias="dataTypes">Типи даних</key>
    <key alias="dictionary">Словник</key>
    <key alias="installedPackages">Встановлені пакети</key>
    <key alias="installSkin">Встановити тему</key>
    <key alias="installStarterKit">Встановити стартовий набір</key>
    <key alias="languages">Мови</key>
    <key alias="localPackage">Встановити локальний пакет</key>
    <key alias="macros">Макроси</key>
    <key alias="media">Медіа-матеріали</key>
    <key alias="mediaTypes">Типи медіа-матеріалів</key>
    <key alias="member">Учасники</key>
    <key alias="memberGroups">Групи учасників</key>
    <key alias="memberRoles">Ролі учасників</key>
    <key alias="memberTypes">Типи учасників</key>
    <key alias="documentTypes">Типи документів</key>
    <key alias="packager">Пакети доповнень</key>
    <key alias="packages">Пакети доповнень</key>
    <key alias="partialViews">Часткові представлення</key>
    <key alias="partialViewMacros">Файли макросів</key>
    <key alias="relationTypes">Типи зв'язків</key>
    <key alias="repositories">Встановити з репозиторію</key>
    <key alias="runway">Встановити Runway</key>
    <key alias="runwayModules">Модулі Runway </key>
    <key alias="scripting">Файли скриптів</key>
    <key alias="scripts">Скрипти</key>
    <key alias="stylesheets">Стилі CSS</key>
    <key alias="templates">Шаблони</key>
    <key alias="users">Користувачі</key>
  </area>
  <area alias="update">
    <key alias="updateAvailable">Доступні оновлення</key>
    <key alias="updateDownloadText">Оновлення %0% готово, натисніть для завантаження</key>
    <key alias="updateNoServer">Немає зв'язку із сервером</key>
    <key alias="updateNoServerError">Під час перевірки оновлень виникла помилка. Будь ласка, перегляньте журнал для отримання додаткової інформації.</key>
  </area>
  <area alias="user">
    <key alias="access">Доступ</key>
    <key alias="accessHelp">На підставі встановлених груп та призначених початкових вузлів, користувач має доступ до наступних вузлів.</key>
    <key alias="administrators">Адміністратор</key>
    <key alias="assignAccess">Призначення доступу</key>
    <key alias="backToUsers">Повернутись до користувачів</key>
    <key alias="categoryField">Поле категорії</key>
    <key alias="change">Змінити</key>
    <key alias="changePassword">Змінити пароль</key>
    <key alias="changePasswordDescription">Ви можете змінити свій пароль для доступу до адміністративної панелі Umbraco, заповнивши нижченаведені поля та натиснувши кнопку 'Змінити пароль'</key>
    <key alias="changePhoto">Змінити аватар</key>
    <key alias="confirmNewPassword">Підтвердження нового пароля</key>
    <key alias="contentChannel">Канал вмісту</key>
    <key alias="createAnotherUser">Створити ще одного користувача</key>
    <key alias="createDate">Створено</key>
    <key alias="createUser">Створити користувача</key>
    <key alias="createUserHelp">Створюйте нових користувачів, яким потрібний доступ до адміністративної панелі Umbraco. При створенні користувача генерується новий первинний пароль, який потрібно повідомити користувачеві.</key>
    <key alias="descriptionField">Поле опису</key>
    <key alias="disabled">Вимкнути користувача</key>
    <key alias="documentType">Тип документа</key>
    <key alias="editors">Редактор</key>
    <key alias="excerptField">Виключити поле</key>
    <key alias="failedPasswordAttempts">Невдалих спроб входу</key>
    <key alias="goToProfile">До профілю користувача</key>
    <key alias="groupsHelp">Додайте користувача до групи(ів), щоб визначити права доступу</key>
    <key alias="inviteEmailCopySubject">Запрошення до панелі адміністрування Umbraco</key>
    <key alias="inviteEmailCopyFormat"><![CDATA[<html><body><p>Здравствуйте, %0%,<br><br>Ви були запрошені користувачем %1%, та Вам надано доступ до панелі адміністрування Umbraco.</p><p>Повідомлення від %1%: <em>%2%</em></p><p>Перейдіть по <a href="%3%" target="_blank" rel="noopener">цьому посиланні</a>, щоб прийняти запрошення.</p><p><small>Якщо ви не можете перейти за посиланням, скопіюйте нижченаведений текст посилання і вставте в адресний рядок вашого браузера.<br/><br/>%3%</small></p></body></html>]]><![CDATA[
        <html>
			<head>
				<meta name='viewport' content='width=device-width'>
				<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
			</head>
			<body class='' style='font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; color: #392F54; line-height: 22px; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background: #1d1333; margin: 0; padding: 0;' bgcolor='#1d1333'>
				<style type='text/css'> @media only screen and (max-width: 620px) {table[class=body] h1 {font-size: 28px !important; margin-bottom: 10px !important; } table[class=body] .wrapper {padding: 32px !important; } table[class=body] .article {padding: 32px !important; } table[class=body] .content {padding: 24px !important; } table[class=body] .container {padding: 0 !important; width: 100% !important; } table[class=body] .main {border-left-width: 0 !important; border-radius: 0 !important; border-right-width: 0 !important; } table[class=body] .btn table {width: 100% !important; } table[class=body] .btn a {width: 100% !important; } table[class=body] .img-responsive {height: auto !important; max-width: 100% !important; width: auto !important; } } .btn-primary table td:hover {background-color: #34495e !important; } .btn-primary a:hover {background-color: #34495e !important; border-color: #34495e !important; } .btn  a:visited {color:#FFFFFF;} </style>
				<table border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #1d1333;" bgcolor="#1d1333">
					<tr>
						<td style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding: 24px;" valign="top">
							<table style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;">
								<tr>
									<td background="https://umbraco.com/umbraco/assets/img/application/logo.png" bgcolor="#1d1333" width="28" height="28" valign="top" style="font-family: sans-serif; font-size: 14px; vertical-align: top;">
										<!--[if gte mso 9]> <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:30px;height:30px;"> <v:fill type="tile" src="https://umbraco.com/umbraco/assets/img/application/logo.png" color="#1d1333" /> <v:textbox inset="0,0,0,0"> <![endif]-->
										<div> </div>
										<!--[if gte mso 9]> </v:textbox> </v:rect> <![endif]-->
									</td>
									<td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top"></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<table border='0' cellpadding='0' cellspacing='0' class='body' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background: #1d1333;' bgcolor='#1d1333'>
					<tr>
						<td style='font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'> </td>
						<td class='container' style='font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; max-width: 560px; width: 560px; margin: 0 auto; padding: 10px;' valign='top'>
							<div class='content' style='box-sizing: border-box; display: block; max-width: 560px; margin: 0 auto; padding: 10px;'>
								<br>
								<table class='main' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; border-radius: 3px; background: #FFFFFF;' bgcolor='#FFFFFF'>
									<tr>
										<td class='wrapper' style='font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 50px;' valign='top'>
											<table border='0' cellpadding='0' cellspacing='0' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;'>
												<tr>
													<td style='line-height: 24px; font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'>
														<h1 style='color: #392F54; font-family: sans-serif; font-weight: bold; line-height: 1.4; font-size: 24px; text-align: left; text-transform: capitalize; margin: 0 0 30px;' align='left'>
															Вітаємо, %0%,
														</h1>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															Ви були запрошені користувачем <a href="mailto:%4%" style="text-decoration: underline; color: #392F54; -ms-word-break: break-all; word-break: break-all;">%1%</a> в панель адміністрування веб-сайту.
														</p>
														<p style='color: #392F54; font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0 0 15px;'>
															Повідомлення від користувача <a href="mailto:%1%" style="text-decoration: none; color: #392F54; -ms-word-break: break-all; word-break: break-all;">%1%</a>:
															<br/>
															<em>%2%</em>
														</p>
														<table border='0' cellpadding='0' cellspacing='0' class='btn btn-primary' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;'>
															<tbody>
																<tr>
																	<td align='left' style='font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px;' valign='top'>
																		<table border='0' cellpadding='0' cellspacing='0' style='border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;'>
																			<tbody>
																				<tr>
																					<td style='font-family: sans-serif; font-size: 14px; vertical-align: top; border-radius: 5px; text-align: center; background: #35C786;' align='center' bgcolor='#35C786' valign='top'>
																						<a href='%3%' target='_blank' rel='noopener' style='color: #FFFFFF; text-decoration: none; -ms-word-break: break-all; word-break: break-all; border-radius: 5px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 14px; font-weight: bold; text-transform: capitalize; background: #35C786; margin: 0; padding: 12px 30px; border: 1px solid #35c786;'>
																							Натисніть на це посилання, щоб прийняти запрошення
																						</a>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
														<p style='max-width: 400px; display: block; color: #392F54; font-family: sans-serif; font-size: 14px; line-height: 20px; font-weight: normal; margin: 15px 0;'>Якщо Ви не маєте можливості натиснути на посилання, скопіюйте наступну адресу (URL) та вставте в адресний рядок Вашого браузера:</p>
															<table border='0' cellpadding='0' cellspacing='0'>
																<tr>
																	<td style='-ms-word-break: break-all; word-break: break-all; font-family: sans-serif; font-size: 11px; line-height:14px;'>
																		<font style="-ms-word-break: break-all; word-break: break-all; font-size: 11px; line-height:14px;">
																			<a style='-ms-word-break: break-all; word-break: break-all; color: #392F54; text-decoration: underline; font-size: 11px; line-height:15px;' href='%3%'>%3%</a>
																		</font>
																	</td>
																</tr>
															</table>
														</p>
													</td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
								<br><br><br>
							</div>
						</td>
						<td style='font-family: sans-serif; font-size: 14px; vertical-align: top;' valign='top'> </td>
					</tr>
				</table>
			</body>
      </html>
    ]]></key>
    <key alias="inviteAnotherUser">Запросити ще одного користувача</key>
    <key alias="inviteUser">Запросити користувача</key>
    <key alias="inviteUserHelp">Запросіть нових користувачів, яким потрібний доступ до адміністративної панелі Umbraco. Запрошеному буде надіслано електронного листа з інструкціями щодо доступу до Umbraco.</key>
    <key alias="language">Мова</key>
    <key alias="languageHelp">Встановіть мову відображення інтерфейсу адміністрування</key>
    <key alias="lastLockoutDate">Час останнього блокування</key>
    <key alias="lastLogin">Час останнього входу</key>
    <key alias="lastPasswordChangeDate">Пароль востаннє змінювався</key>
    <key alias="loginname">Ім'я входу (логін)</key>
    <key alias="mediastartnode">Початковий вузол медіа-бібліотеки</key>
    <key alias="mediastartnodehelp">Можна обмежити доступ до медіа-бібліотеки (якійсь її частини), задавши початковий вузол</key>
    <key alias="mediastartnodes">Початкові вузли медіа-бібліотеки</key>
    <key alias="mediastartnodeshelp">Можна обмежити доступ до медіа-бібліотеки (якихось її частин), задавши перелік початкових вузлів.</key>
    <key alias="modules">Розділи</key>
    <key alias="newPassword">Новий пароль</key>
    <key alias="noConsole">Вимкнути доступ до адміністративної панелі Umbraco</key>
    <key alias="noLogin">поки що не входив</key>
    <key alias="noLockouts">поки не блокувався</key>
    <key alias="noPasswordChange">Пароль не змінювався</key>
    <key alias="oldPassword">Попередній пароль</key>
    <key alias="password">Пароль</key>
    <key alias="passwordChanged">Ваш пароль доступу змінено!</key>
    <key alias="passwordConfirm">Підтвердіть новий пароль</key>
    <key alias="passwordCurrent">Поточний пароль</key>
    <key alias="passwordEnterNew">Вкажіть новий пароль</key>
    <key alias="passwordInvalid">Поточний пароль вказано неправильно</key>
    <key alias="passwordIsBlank">Пароль не може бути порожнім</key>
    <key alias="passwordIsDifferent">Новий пароль та його підтвердження не збігаються. Спробуйте ще раз</key>
    <key alias="passwordMismatch">Новий пароль та його підтвердження не співпадають</key>
    <key alias="permissionReplaceChildren">Замінити дозволи для дочірніх документів</key>
    <key alias="permissionSelectedPages">Ви змінюєте дозволи для таких документів:</key>
    <key alias="permissionSelectPages">Виберіть документи, щоб змінити їх дозволи</key>
    <key alias="removePhoto">Видалити аватар</key>
    <key alias="permissionsDefault">Права доступу за замовчуванням</key>
    <key alias="permissionsGranular">Атомарні права доступу</key>
    <key alias="permissionsGranularHelp">Можна встановити права доступу до конкретних вузлів</key>
    <key alias="profile">Профіль</key>
    <key alias="resetPassword">Скинути пароль</key>
    <key alias="searchAllChildren">Пошук усіх дочірніх документів</key>
    <key alias="selectUserGroups">Вибрати групи користувачів</key>
    <key alias="sendInvite">Відправити запрошення</key>
    <key alias="sessionExpires" version="7.0">Сесія закінчується через</key>
    <key alias="sectionsHelp">Розділи, доступні користувачеві</key>
    <key alias="noStartNode">Початковий вузол не заданий</key>
    <key alias="noStartNodes">Початкові вузли не задані</key>
    <key alias="startnode">Початковий вузол вмісту</key>
    <key alias="startnodehelp">Можна обмежити доступ до дерева вмісту (будь-якої його частини), задавши початковий вузол</key>
    <key alias="startnodes">Початкові вузли вмісту</key>
    <key alias="startnodeshelp">Можна обмежити доступ до дерева вмісту (якимось його частинам), задавши перелік початкових вузлів</key>
    <key alias="userCreated">Був створений</key>
    <key alias="userCreatedSuccessHelp">Новий первинний пароль успішно згенеровано. Для входу використовуйте наведений нижче пароль.</key>
    <key alias="updateDate">Час останнього оновлення</key>
    <key alias="username">Ім'я користувача</key>
    <key alias="usergroup">Група користувачів</key>
    <key alias="userInvited"> був запрошений</key>
    <key alias="userInvitedSuccessHelp">Новому користувачеві було надіслано запрошення, яке містить інструкції для входу в панель Umbraco.</key>
    <key alias="userinviteWelcomeMessage">Привіт і ласкаво просимо до Umbraco! Все буде готове протягом декількох хвилин, нам потрібно задати пароль для входу.</key>
    <key alias="userManagement">Управління користувачами</key>
    <key alias="userPermissions">Дозволи для користувача</key>
    <key alias="writer">Автор</key>
    <key alias="yourHistory" version="7.0">Ваша нещодавня активність</key>
    <key alias="yourProfile" version="7.0">Ваш профіль</key>
  </area>
  <area alias="validation">
    <key alias="validation">Валідація</key>
    <key alias="validateAsEmail">Валідація за форматом email</key>
    <key alias="validateAsNumber">Валідація числового значення</key>
    <key alias="validateAsUrl">Валідація за форматом URL</key>
    <key alias="enterCustomValidation">...або вказати свої правила валідації</key>
    <key alias="fieldIsMandatory">Обов'язково для заповнення</key>
    <key alias="validationRegExp">Задайте регулярний вираз</key>
    <key alias="minCount">Необхідно вибрати як мінімум</key>
    <key alias="maxCount">Можливо вибрати максимум</key>
    <key alias="items">елементів</key>
    <key alias="itemsSelected">елементів</key>
    <key alias="invalidDate">Невірний формат дати</key>
    <key alias="invalidNumber">Не є числом</key>
    <key alias="invalidEmail">Невірний формат email-адреси</key>
  </area>
  <area alias="logViewer">
    <key alias="selectAllLogLevelFilters">Вибрати все</key>
    <key alias="deselectAllLogLevelFilters">Прибрати виділення з усього</key>
  </area>
</language>
