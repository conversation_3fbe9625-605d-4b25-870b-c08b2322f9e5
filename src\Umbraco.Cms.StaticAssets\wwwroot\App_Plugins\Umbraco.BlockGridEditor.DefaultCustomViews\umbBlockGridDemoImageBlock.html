<style>

    :host {
        min-height: 160px;
    }
    
    button > * {
        vertical-align: middle;
    }
    button {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        cursor: pointer;
        color: black;
        background-color: transparent;
        text-align: left;
        padding: 0;
        user-select: none;
        border: none;
        transition: border-color 120ms, background-color 120ms;
        max-height: 80vh;
        overflow:hidden;
    }
    button:hover {
        color: #2152A3;
    }
    img {
        object-fit: cover;
        height: 100%;
        width: 100%;
        flex-grow: 0;
    }

    .icon {
        font-size: 60px;
        display: block;
    }

    .umb-icon {
        display: inline-block;
        width: 1em;
        height: 1em;
        flex-shrink: 0;
    }

    .umb-icon svg {
        width: 100%;
        height: 100%;
        fill: currentColor;
        animation: inherit;
    }

    .file-name {
        background-color: rgba(255, 255, 255, 0.8);
        color: black;
        padding: 3px 6px;
        position: absolute;
        bottom: 0;
        right: 0;

        opacity: var(--umb-block-grid--block-ui-opacity, 0);
        transition: opacity 120ms;
    }

    .is-trashed {
        background-color: #d42054 !important;
        color:white !important;
    }
    .is-trashed .file-name {
        opacity: 1;
    }

</style>

<button type="button" ng-click="block.edit()" ng-focus="block.focus" ng-class="{'is-trashed': mediaItem.trashed}">
    {{mediaItem = (block.data.image[0].mediaKey | mediaItemResolver); ""}}
    <img ng-if="mediaItem !== null && (mediaItem.mediaLink.indexOf('jpg') !== -1 || mediaItem.mediaLink.indexOf('png') !== -1 || mediaItem.mediaLink.indexOf('webp') !== -1 || mediaItem.mediaLink.indexOf('gif') !== -1) && !mediaItem.trashed" ng-src="{{mediaItem.mediaLink}}" alt="{{mediaItem.name}}" />
    <umb-icon ng-if="mediaItem !== null && mediaItem.mediaLink.indexOf('jpg') === -1 && mediaItem.mediaLink.indexOf('png') === -1 && mediaItem.mediaLink.indexOf('webp') === -1 && mediaItem.mediaLink.indexOf('gif') === -1 || mediaItem.trashed" icon="{{mediaItem.contentType.icon}}" class="icon"></umb-icon>
    <span ng-if="mediaItem !== null && mediaItem.name" class="file-name">
        <span ng-if="mediaItem.trashed"><localize key="mediaPicker_trashed">Trashed</localize>:</span>
        {{mediaItem.name}}
    </span>
</button>