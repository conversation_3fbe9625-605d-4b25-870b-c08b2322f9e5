<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Title>Umbraco CMS - Imaging - ImageSharp</Title>
    <Description>Adds imaging support using ImageSharp/ImageSharp.Web to Umbraco CMS.</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SixLabors.ImageSharp" />
    <PackageReference Include="SixLabors.ImageSharp.Web" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Umbraco.Web.Common\Umbraco.Web.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Umbraco.Tests.UnitTests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
</Project>
